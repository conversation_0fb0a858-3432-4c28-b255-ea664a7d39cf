import os
import uuid
from datetime import datetime

import docker
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.responses import PlainTextResponse
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.log import logger
from app.db.session import get_session
from app.models.task import Task
from app.models.yaml_generator import C2netConfig, generate_yaml, load_config
from app.utils.some_function import clear_task_data
from fastapi.responses import JSONResponse
from app.services.log_parser_service import LogParserService

router = APIRouter()


class YamlGenerateResponse(BaseModel):
    status: str
    filename: str
    yaml_content: str


def execute_in_ns3_container(filename):
    try:
        base_filename = os.path.splitext(filename)[0]
        log_filename = f"{base_filename}.log"
        shell_command = (
            f"cd /code/ns3-c2net && "
            f"./ns3 run 'c2net-example-yaml --c2netConfigPath=/code/ns3-c2net/generated_configs/{filename}' "
            f"2>&1 | tee /code/ns3-c2net/logs/{log_filename}"
        )

        logger.info(f"Starting command in NS3 container: {shell_command}")
        if settings.debug:
            # Print what would happen or use a mock implementation
            logger.info("DEBUG MODE: Would execute container command")
            # Optionally add some mock implementation for testing
        else:
            client = docker.from_env()
            container = client.containers.get("c2net-sim-ns3")
            container.exec_run(["/bin/bash", "-c", shell_command], privileged=True, detach=True)
            logger.info(f"Command started successfully. Results will be written to: {log_filename}")
        return True
    except Exception as e:
        logger.error(f"Failed to execute in container: {str(e)}")
        return False


async def create_task_from_config(config: C2netConfig, session: AsyncSession):
    date_str = datetime.now().strftime("%Y%m%d-%H%M%S")
    filename = f"c2net-config-GBA-{date_str}-{str(uuid.uuid4())[:8]}.yaml"

    if config.backendConfig.taskId != 0:
        # This is an update to an existing task
        existing_task = await session.get(Task, config.backendConfig.taskId)
        if existing_task:
            # Update the existing task with new information
            existing_task.remark = config.backendConfig.remark
            existing_task.n_centers = len(config.c2netComputingCenterList)
            existing_task.strategy = config.scheduleConfig.scheduleType
            existing_task.yaml_filename = filename
            existing_task.completed_flag = False  # Reset completion flag for new run
            existing_task.params = config.params  # 更新params字段
            await clear_task_data(session, existing_task.id)
            # Save the updated task
            await session.commit()
            await session.refresh(existing_task)
            logger.info(f"Updated task record with ID: {existing_task.id} for file: {filename}")
            yaml_str = generate_yaml(config, "generated_configs", filename)
            return filename, yaml_str
        else:
            logger.warning(f"Task with ID {config.backendConfig.taskId} not found, creating new task")
            # Fall through to create new task
    # Create a new task
    task = Task(
        remark=config.backendConfig.remark,
        n_centers=len(config.c2netComputingCenterList),
        n_jobs=0,
        n_pops=0,
        completed_flag=False,
        strategy=config.scheduleConfig.scheduleType,
        yaml_filename=filename,
        params=config.params,  # 添加params字段
    )
    # Add to database and commit
    task = await session.merge(task)
    await session.commit()
    await session.refresh(task)
    logger.info(f"Created task record with ID: {task.id} for file: {filename}")
    config.backendConfig.taskId = task.id
    yaml_str = generate_yaml(config, "generated_configs", filename)
    return filename, yaml_str


@router.post("/generate", response_model=YamlGenerateResponse)
async def generate_yaml_file(
        config: C2netConfig,
        preview: bool = False,
        session: AsyncSession = Depends(get_session),  # Fix: add get_session
):
    try:
        date_str = datetime.now().strftime("%Y%m%d-%H%M%S")
        success = True
        if preview:
            filename = f"c2net-config-GBA-Preview-{date_str}-{str(uuid.uuid4())[:8]}.yaml"
            yaml_str = generate_yaml(config, "generated_preview_configs", filename)
        else:
            filename, yaml_str = await create_task_from_config(config, session)
            success = execute_in_ns3_container(filename)
        response = {
            "status": "preview" if preview else ("success" if success else "partial_success"),
            "filename": filename,
            "yaml_content": yaml_str
        }

        return response
    except Exception as e:
        info = f"Failed to generate YAML: {str(e)}"
        logger.error(info)
        raise HTTPException(status_code=500, detail=info)


@router.get("/logs/{filename}", response_class=PlainTextResponse)
async def get_simulation_log(filename: str):
    try:
        if not filename.endswith('.yaml'):
            raise HTTPException(status_code=400, detail="Invalid file name")
        base_filename = os.path.splitext(filename)[0]
        log_filename = f"{base_filename}.log"

        log_file_path = os.path.join("c2net_sim_ns3_logs", log_filename)

        if not os.path.exists(log_file_path):
            raise HTTPException(status_code=404, detail=f"Log file not found: {log_filename}")

        with open(log_file_path, 'r') as file:
            log_content = file.read()

        return log_content
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        logger.error(f"Failed to read log file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to read log file: {str(e)}")


@router.get("/load/{task_id}")
async def load_yaml_file(
        task_id: str,
        session: AsyncSession = Depends(get_session),
):
    try:
        task = await session.get(Task, task_id)
        file_path = os.path.join("generated_configs", task.yaml_filename)

        if not os.path.exists(file_path):
            config = None
        else:
            logger.info(file_path)
            config = load_config(file_path)
        return {
            "task": task,
            "yaml_data": config,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to load YAML: {str(e)}")


@router.get("/task_log/{task_id}", response_class=PlainTextResponse)
async def get_task_execution_log(
        task_id: str,
        session: AsyncSession = Depends(get_session),
        max_lines: int = None,
        start_line: int = 0,
        tail: int = None,
        stream: bool = False,
):
    """Get task logs with options for pagination and streaming"""
    try:
        # Validate and get log path
        task = await session.get(Task, task_id)
        if not task:
            raise HTTPException(status_code=404, detail=f"Task not found: {task_id}")

        log_file_path = os.path.join("c2net_sim_ns3_logs", f"{os.path.splitext(task.yaml_filename)[0]}.log")

        if not os.path.exists(log_file_path):
            raise HTTPException(status_code=404, detail=f"Log file not found for task: {task_id}")

        # Helper functions for log retrieval
        def get_tail(file_path, n_lines):
            from collections import deque
            result = deque(maxlen=n_lines)
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    result.append(line)
            return ''.join(result)

        def get_paginated(file_path, skip, max_count):
            with open(file_path, 'r', encoding='utf-8') as f:
                # Skip initial lines
                for _ in range(skip):
                    if not f.readline():
                        break

                # Read requested number of lines
                if max_count:
                    lines = []
                    for _ in range(max_count):
                        line = f.readline()
                        if not line:
                            break
                        lines.append(line)
                    return ''.join(lines)
                else:
                    return f.read()

        async def stream_content(file_path):
            # For streaming response
            with open(file_path, 'r', encoding='utf-8') as f:
                if tail:
                    # For tail mode, first collect the last n lines
                    from collections import deque
                    last_lines = deque(maxlen=tail)
                    for line in f:
                        last_lines.append(line)

                    # Then yield them
                    for line in last_lines:
                        yield line
                else:
                    # Skip lines if needed
                    for _ in range(start_line):
                        if not f.readline():
                            return

                    # Stream with optional line limit
                    count = 0
                    for line in f:
                        yield line
                        count += 1
                        if max_lines and count >= max_lines:
                            break

        # Choose appropriate response type
        if stream:
            from fastapi.responses import StreamingResponse
            return StreamingResponse(stream_content(log_file_path), media_type="text/plain")
        else:
            if tail:
                return get_tail(log_file_path, tail)
            else:
                return get_paginated(log_file_path, start_line, max_lines)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to read log file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to read log file: {str(e)}")


@router.get("/timeline/{task_id}")
async def get_timeline_data(
        task_id: str,
        session: AsyncSession = Depends(get_session)
):

    try:
        task = await session.get(Task, task_id)
        if not task:
            raise HTTPException(status_code=404, detail=f"Task not found: {task_id}")

        file_path = os.path.join("c2net_sim_ns3_logs", f"{os.path.splitext(task.yaml_filename)[0]}.log")

        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail=f"Log file not found for task: {task_id}")

        service = LogParserService()
        result = service.parse_log_file(file_path)

        if not result.success:
            raise HTTPException(status_code=400, detail=f"解析失败: {result.error_message}")

        timeline_data = result.timeline_data

        return JSONResponse(content={
            "success": True,
            "message": "获取时序图数据成功",
            "data": timeline_data.model_dump()
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取时序图数据API错误: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")



