<!-- Use preprocessors via the lang attribute! e.g. <template lang="pug"> -->
<template>
    <div id="app">
        <h1><PERSON>hart Gantt</h1>

        <div id="chart"></div>

    </div>
</template>

<script>
export default {
  data () {
    return {
      mockData: [],
      ganttChart: undefined,
      person: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
      type: [
        { name: 'Rest', color: '#64CCAF' },
        { name: 'Dining', color: '#63A6F7' },
        { name: 'Meeting', color: '#F7C83E' }
      ],
      minOnlinePersonNum: 2,

      chartOptions: {
        yAxis: {
          data: []
        },
        xAxis: {
          // type: 'time',
          position: 'top',
          scale: true,
          splitLine: {
            lineStyle: {
              color: ['#E9EDFF']
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            lineStyle: {
              color: '#929ABA'
            }
          },
          axisLabel: {
            color: '#929ABA',
            inside: false,
            align: 'center',
            formatter: function (val) {
              // console.log({val})
              return moment(val).format('YYYY/MM/DD HH:mm')
            }
          }
        },
        dataZoom: [{
          type: 'slider',
          filterMode: 'weakFilter', // 當所有數據都 overflow 才過濾掉
          xAxisIndex: 0,
          height: 10,
          bottom: 10,
          showDetail: false,
          handleSize: '80%'
        }, {
          type: 'inside',
          yAxisIndex: 1,
          start: 0,
          end: 3,
          zoomOnMouseWheel: false,
          moveOnMouseWheel: true
        }]
      }
    }
  },
  // computed: {
  //     lackOfOnlinePersonTimeRange () {
  //       let allTimePoints = []
  //       this.mockData.forEach(d => {
  //         const start = d.value[1];
  //         const end = d.value[2];
  //         allTimePoints = [...allTimePoints, start, end]
  //       })
  //       // 列出所有時間點
  //       const sortedTimePoints = allTimePoints.sort()
  //       const timePointCounter = {}
  //       sortedTimePoints.forEach(key => {
  //         timePointCounter[key] = 0
  //       })

  //       // 算每個時間點的人數
  //       const timePoints = Object.keys(timePointCounter)
  //       this.mockData.forEach(d => {
  //         const start = d.value[1];
  //         const end = d.value[2];
  //         timePoints.forEach(tp => {
  //           if (start <= tp && tp < end) {
  //             timePointCounter[tp] += 1
  //           }
  //         })
  //       })

  //       const numOfPerson = this.person.length
  //       const lackOfPerson = []
  //       let timeRange = { start: 0, end: 0 }
  //       timePoints.forEach(tp => {
  //         console.log(numOfPerson, timePointCounter[tp], this.minOnlinePersonNum)
  //         const isLack = (numOfPerson - timePointCounter[tp]) < this.minOnlinePersonNum
  //         if (isLack) {
  //           console.log({isLack})
  //           if (timeRange.start === 0) {
  //             timeRange.start = tp
  //           } else {
  //             timeRange.end = tp
  //           }
  //           console.log({timeRange})
  //         } else {
  //           if (timeRange.start !== 0 && timeRange.end !== 0) {
  //             lackOfPerson.push(Object.assign({}, timeRange))
  //             timeRange = { start: 0, end: 0 }
  //           }
  //         }
  //       })

  //       if(timeRange.start !== 0 && timeRange.end !== 0) {
  //         lackOfPerson.push(Object.assign({}, timeRange)
  //       }
  //       return lackOfPerson
  //     },
  //   },
  methods: {
    resize () {
      this.ganttChart.resize()
    },
    renderItem (params, api) {
      var categoryIndex = api.value(0)
      var start = api.coord([api.value(1), categoryIndex])
      var end = api.coord([api.value(2), categoryIndex])
      var height = api.size([0, 1])[1] * 0.6

      var rectShape = echarts.graphic.clipRectByRect({
        x: start[0],
        y: start[1] - height / 2,
        width: end[0] - start[0],
        height: height
      }, {
        x: params.coordSys.x,
        y: params.coordSys.y,
        width: params.coordSys.width,
        height: params.coordSys.height
      })

      return rectShape && {
        type: 'rect',
        transition: ['shape'],
        shape: rectShape,
        style: api.style()
      }
    },
    setUpChart () {
      this.chartOptions.yAxis.data = this.person
      this.chartOptions.series = [{
        type: 'custom',
        renderItem: this.renderItem,
        encode: { // data 維度映射
          x: [1, 2],
          y: 0
        },
        // markArea:{
        //   silent: true,
        //   data: this.lackOfOnlinePersonTimeRange.map((timeRange) => {
        //     return [{xAxis: timeRange.start}, {xAxis: timeRange.end}];
        //   }),
        // },
        data: this.mockData
      }]
      // console.log(this.chartOptions)
      this.ganttChart.setOption(this.chartOptions, true)
    },

    genMockData () {
      const mockData = []
      const today = new Date()
      const startTime = today.getTime()
      this.person.forEach((p, index) => {
        let baseTime = startTime
        for (var i = 0; i < 5; i++) {
          var typeItem = this.type[Math.round(Math.random() * (this.type.length - 1))]
          var duration = Math.round(Math.random() * 6000000)
          mockData.push({
            name: typeItem.name,
            value: [
              index,
              baseTime, // startTime
              baseTime += duration, // endTime
              duration,
              typeItem.name
            ],
            itemStyle: {
              normal: {
                color: typeItem.color
              }
            }
          })
          baseTime += Math.round(Math.random() * 2000000)
        }
      })

      return mockData
    }
  },

  mounted () {
    this.mockData = this.genMockData()
    // console.log(this.mockData)
    console.log({ lack: this.lackOfOnlinePersonTimeRange })

    this.ganttChart = echarts.init(document.getElementById('chart'))
    this.setUpChart()

    window.addEventListener('resize', this.resize)
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.resize)
  }
}
</script>

<!-- Use preprocessors via the lang attribute! e.g. <style lang="scss"> -->
<style>
#app {
    font-family: Avenir, Helvetica, Arial, sans-serif;
    text-align: center;
    color: #2c3e50;
    margin-top: 60px;
    height: 100%;
}

#chart {
    border: 1px solid hotpink;
    height: 300px;
}
</style>
