/**
 * 动画控制模块
 * 负责动画的开始、暂停、继续等控制逻辑
 */

import MultiTaskService from '@/services/MultiTaskService'

export default {
  methods: {
    /**
     * 启动多任务动画定时器
     */
    startMultiTaskAnimationTimer(Time, historySubmitJob, historyCompleteJob, city, submitJobUsed, pendingJobUsed, resouceCompareUsed, xAxis, tasksData) {
      const that = this
      this.timer = setInterval(() => {
        that.count++
        if (that.count == that.total) {
          clearInterval(that.timer)
          return
        }

        // 更新动画计时器数据（与单任务模式保持一致）
        that.updateAnimationTimerData(Time, historySubmitJob, historyCompleteJob, xAxis)
        
        // 更新右侧多任务对比图表
        that.processMultipleTasksCompareData(tasksData)

        // 重绘图表
        that.drawLine2()
        that.drawLine()
        that.showStart = true
      }, 2000)
    },

    /**
     * 开始动画计时器的通用方法
     */
    startAnimationTimer(Time, historySubmitJob, historyCompleteJob, city, submitJobUsed, pendingJobUsed, resouceCompareUsed, xAxis) {
      const that = this
      this.timer = setInterval(() => {
        that.count++
        if (that.count == that.total) {
          clearInterval(that.timer)
          return
        }

        that.updateAnimationTimerData(Time, historySubmitJob, historyCompleteJob, xAxis)
        that.updateTaskDataByType(city, submitJobUsed, pendingJobUsed, resouceCompareUsed)

        that.drawLine2()
        that.drawLine()
        that.showStart = true
      }, 2000)
    },

    /**
     * 更新动画计时器数据
     */
    updateAnimationTimerData(Time, historySubmitJob, historyCompleteJob, xAxis) {
      // 注意：这里传入的Time已经是小时值了，因此不需要再处理
      this.msg.SnapshotTime = Time[this.count] + 'h'
      this.msg2.totalNum = historySubmitJob[this.count]
      this.msg2.execNum = historyCompleteJob[this.count]
      this.Data.xAxis = xAxis
      this.runningTime = Time[this.count] + 'h'
    },

    /**
     * 开始或重新开始动画
     */
    start() {
      this.prepareForStart()

      // 检查是否有选中的任务
      if (this.selectedTaskIds.length > 1) {
        // 多任务模式：使用多任务服务获取数据，但折线图显示主任务数据
        console.log(`开始处理${this.selectedTaskIds.length}个任务:`, this.selectedTaskIds)
        
        // 使用新的多任务服务获取数据
        MultiTaskService.getMultipleTasksData(this.selectedTaskIds, this.interval)
          .then(tasksData => {
            if (tasksData && tasksData.length > 0) {
              console.log('获取到多个任务数据:', tasksData.map(t => t.ID))
              
              // 处理多任务数据（折线图显示主任务数据，右侧图表支持多任务对比）
              this.processMultipleTasksData(tasksData)
              
              // 设置数据已加载标记
              this.dataAlreadyLoaded = true

              // 确保地图更新
              this.$nextTick(() => {
                if (this.$refs.chinaMap) {
                  console.log('多任务数据已加载完成，通知地图组件刷新')
                  this.$refs.chinaMap._preventReinitOnNextUpdate = false
                  
                  // 使用第一个任务的数据更新地图
                  if (tasksData[0]) {
                    this.taskIdRes = tasksData[0]
                  }
                }
              })
            }
          })
          .catch(error => {
            console.error('获取多任务数据失败:', error)
            this.$message.error('获取任务数据失败，请稍后重试')
            this.disable = false
          })
      } else if (this.taskId !== 0) {
        // 单任务模式：使用原有的单任务处理逻辑
        this.getJobData().then(() => {
          if (this.taskIdRes) {
            this.processJobData(this.taskIdRes, this.compareIdRes)
            this.dataAlreadyLoaded = true

            this.$nextTick(() => {
              if (this.$refs.chinaMap) {
                console.log('数据已加载完成，通知地图组件刷新')
                this.$refs.chinaMap._preventReinitOnNextUpdate = false

                if (!this.taskIdRes) {
                  this.$refs.chinaMap.getJobDetail(this.taskId)
                }
              }
            })
          }
        })
      } else {
        console.log('没有选中任何任务')
        this.disable = false
      }
    },

    /**
     * 准备开始
     */
    prepareForStart() {
      this.disable = true
      clearInterval(this.timer)
      this.count = 0
      // 重置数据加载状态
      this.dataAlreadyLoaded = false

      // 重要：使用nextTick机制确保DOM更新后再修改状态
      this.$nextTick(() => {
        // 如果chinaMap组件存在，确保它的状态保持
        if (this.$refs.chinaMap) {
          // 设置chinaMap的内部状态，但不重建地图
          this.$refs.chinaMap.stopped = false
        }

        // 使用空数据结构，仅保持图表框架
        this.taskData = {
          xData: [],
          used: [],
          used2: [],
          multiTaskData: []
        }

        this.Data = {
          xAxis: [],
          yAxis: []
        }

        // 在设置Stop状态前先绘制图表
        this.drawLine()
        this.drawLine2()

        // 最后才设置Stop状态
        this.Stop = false
        this.buttonShow = true
        this.showButtom = true // 确保显示暂停/继续按钮
      })
    },

    /**
     * 暂停动画
     */
    stop() {
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.nowCount = this.count
      this.buttonShow = false
      this.Stop = true
    },

    /**
     * 继续动画
     */
    goOn() {
      if (this.count == this.total) {
        return
      }

      // 设置基本状态标记
      this.Stop = false
      this.buttonShow = true
      this.count = this.nowCount

      // 通知地图组件恢复动画
      if (this.$refs.chinaMap) {
        console.log('通知地图组件恢复动画')
      }

      // 检查是否已经有加载的数据
      if (this.dataAlreadyLoaded && this.temp && this.Data.xAxis.length > 0) {
        console.log('使用已缓存的数据恢复动画，不需要重新请求')

        // 立即更新当前帧数据 - 这是关键修改部分
        const currentIndex = this.count

        // 立即更新环图数据，确保继续时环图立即更新
        if (this.taskIdRes && this.taskIdRes.CenterInfoToWebList) {
          const centerInfoList = this.taskIdRes.CenterInfoToWebList
          let historySubmitJob = 0
          let historyCompleteJob = 0

          // 从当前快照收集数据
          centerInfoList.forEach(center => {
            if (center.SnapshotInfoToWebList &&
              center.SnapshotInfoToWebList[currentIndex]) {
              historySubmitJob += center.SnapshotInfoToWebList[currentIndex].HistorySubmitJob
              historyCompleteJob += center.SnapshotInfoToWebList[currentIndex].HistoryCompleteJob
            }
          })

          // 更新环图数据
          this.msg2.totalNum = historySubmitJob
          this.msg2.execNum = historyCompleteJob
        }

        // 根据当前选择的任务类型立即更新右侧图表
        if (this.taskType === 1 && this.submitJobCompare0) {
          this.updateTaskDataForType(
            this.submitJobCompare0.xData,
            this.submitJobCompare0.used[currentIndex],
            this.compareId !== 0 && this.submitJobCompare?.used2
              ? this.submitJobCompare.used2[currentIndex] : undefined
          )
        } else if (this.taskType === 2 && this.pendingJobCompare0) {
          this.updateTaskDataForType(
            this.pendingJobCompare0.xData,
            this.pendingJobCompare0.used[currentIndex],
            this.compareId !== 0 && this.pendingJobCompare?.used2
              ? this.pendingJobCompare.used2[currentIndex] : undefined
          )
        } else if (this.taskType === 3 && this.resouceCompare0) {
          this.updateTaskDataForType(
            this.resouceCompare0.xData,
            this.resouceCompare0.used[currentIndex],
            this.compareId !== 0 && this.resouceCompare?.used2
              ? this.resouceCompare.used2[currentIndex] : undefined
          )
        }

        // 立即重绘右侧图表
        this.drawLine2()

        // 清除任何可能存在的定时器
        clearInterval(this.timer)

        // 从当前帧重启定时器
        this.startResumeTimer()
      } else {
        // 没有缓存数据，使用原来的方法
        this.prepareForResume()

        if (this.taskId !== 0) {
          this.getJobData().then(() => {
            if (this.taskIdRes) {
              this.processJobData(this.taskIdRes, this.compareIdRes)
              this.dataAlreadyLoaded = true
            }
          })
        }
      }
    },

    /**
     * 启动恢复定时器
     */
    startResumeTimer() {
      const that = this
      this.timer = setInterval(() => {
        that.count++
        if (that.count >= that.total) {
          clearInterval(that.timer)
          that.count = that.total
          return
        }

        // 更新数据
        // 更新折线图数据
        if (that.temp && that.count < that.temp.length) {
          that.Data.yAxis[that.count] = that.temp[that.count]
        }

        // 更新时间信息
        if (that.Data.xAxis && that.count < that.Data.xAxis.length) {
          that.msg.SnapshotTime = that.Data.xAxis[that.count] + 'h'
          that.runningTime = that.Data.xAxis[that.count] + 'h'
        }

        // 更新环图数据 - 添加对msg2对象的更新，解决环图不更新问题
        if (that.taskIdRes && that.taskIdRes.CenterInfoToWebList) {
          const centerInfoList = that.taskIdRes.CenterInfoToWebList
          let historySubmitJob = 0
          let historyCompleteJob = 0

          // 从当前快照收集数据
          centerInfoList.forEach(center => {
            if (center.SnapshotInfoToWebList &&
              center.SnapshotInfoToWebList[that.count]) {
              historySubmitJob += center.SnapshotInfoToWebList[that.count].HistorySubmitJob
              historyCompleteJob += center.SnapshotInfoToWebList[that.count].HistoryCompleteJob
            }
          })

          // 更新环图数据
          that.msg2.totalNum = historySubmitJob
          that.msg2.execNum = historyCompleteJob
        }

        // 更新右侧任务统计图
        const currentTaskType = that.taskType || 1
        if (currentTaskType === 1 && that.submitJobCompare0) {
          that.updateTaskDataForType(
            that.submitJobCompare0.xData,
            that.submitJobCompare0.used[that.count],
            that.compareId !== 0 && that.submitJobCompare?.used2
              ? that.submitJobCompare.used2[that.count] : undefined
          )
        } else if (currentTaskType === 2 && that.pendingJobCompare0) {
          that.updateTaskDataForType(
            that.pendingJobCompare0.xData,
            that.pendingJobCompare0.used[that.count],
            that.compareId !== 0 && that.pendingJobCompare?.used2
              ? that.pendingJobCompare.used2[that.count] : undefined
          )
        } else if (currentTaskType === 3 && that.resouceCompare0) {
          that.updateTaskDataForType(
            that.resouceCompare0.xData,
            that.resouceCompare0.used[that.count],
            that.compareId !== 0 && that.resouceCompare?.used2
              ? that.resouceCompare.used2[that.count] : undefined
          )
        }

        // 重绘图表
        that.drawLine()
        that.drawLine2()
      }, 2000)
    },

    /**
     * 准备恢复
     */
    prepareForResume() {
      this.Stop = false
      this.buttonShow = true
      this.count = this.nowCount

      // 使用空数据结构，仅保持图表框架
      this.Data = {
        xAxis: [],
        yAxis: []
      }

      this.taskData = {
        xData: [],
        used: [],
        used2: [],
        multiTaskData: []
      }

      // 重置环图数据，避免显示旧数据
      this.msg2 = {
        totalNum: 0,
        execNum: 0
      }
    },

    /**
     * 改变进度
     */
    changeProgress() {
      this.count = Math.floor((this.percentage / 100) * this.total)
      this.nowCount = this.count
    },

    /**
     * 改变时间间隔
     */
    changeInterval() {
      this.$store.commit('changeInterval', this.value)
      this.Stop = true
      this.start()
    },

    /**
     * 格式化提示
     */
    formatTooltip(val) {
      return val + '%'
    }
  }
}
