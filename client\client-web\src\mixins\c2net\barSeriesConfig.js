/**
 * 柱状图系列配置模块
 * 负责创建柱状图的系列配置
 */

export default {
  methods: {
    /**
     * 创建柱状图系列配置
     * @returns {Array} 包含系列配置的数组
     */
    createBarSeriesConfig() {
      const formatter = (param) => {
        if (this.taskType == 1) {
          return param.data || 0
        } else if (this.taskType == 2) {
          // 确保formatDuring2返回的是纯数字或带单位的字符串，以便后续可能的样式处理
          const value = this.formatDuring2(param.data || 0)
          return typeof value === 'number' ? value : value + 'h' // 假设formatDuring2可能返回数字或已格式化的字符串
        } else {
          return (param.data || 0).toFixed(2) + '%'
        }
      }

      const series = []
      
      // 调试信息
      console.log('createBarSeriesConfig 调试信息:')
      console.log('selectedTasks:', this.selectedTasks)
      console.log('selectedTasks.length:', this.selectedTasks?.length)
      console.log('taskData.multiTaskData:', this.taskData.multiTaskData)
      console.log('taskData.multiTaskData.length:', this.taskData.multiTaskData?.length)
      console.log('taskType:', this.taskType)
      console.log('条件判断结果:', this.selectedTasks && this.selectedTasks.length > 0 && this.taskData.multiTaskData && this.taskData.multiTaskData.length > 0)
      
      // 支持多个任务的显示
      if (this.selectedTasks && this.selectedTasks.length > 0 && this.taskData.multiTaskData && this.taskData.multiTaskData.length > 0) {
        console.log('进入多任务模式，将创建', this.taskData.multiTaskData.length, '个系列')
        
        // 使用固定柱子宽度
        const taskCount = this.taskData.multiTaskData.length
        const cityCount = this.taskData.xData ? this.taskData.xData.length : 0
        const fixedBarWidth = this.getFixedBarWidth(taskCount)
        
        // 多任务模式：为每个选中的任务创建一个系列
        this.taskData.multiTaskData.forEach((taskData, index) => {
          console.log(`创建第${index + 1}个系列，任务ID: ${taskData.taskId}, 策略: ${taskData.strategy}`)
          const colors = [
            { start: 'rgba(30, 144, 255, 1)', end: 'rgba(100, 200, 255, 1)', border: 'rgba(130, 220, 255, 0.9)' },
            { start: 'rgba(255, 140, 0, 1)', end: 'rgba(255, 180, 80, 1)', border: 'rgba(255, 200, 120, 0.9)' },
            { start: 'rgba(50, 205, 50, 1)', end: 'rgba(100, 240, 100, 1)', border: 'rgba(130, 255, 130, 0.9)' },
            { start: 'rgba(138, 43, 226, 1)', end: 'rgba(180, 100, 255, 1)', border: 'rgba(200, 140, 255, 0.9)' },
            { start: 'rgba(0, 191, 255, 1)', end: 'rgba(80, 230, 255, 1)', border: 'rgba(120, 250, 255, 0.9)' },
            { start: 'rgba(255, 215, 0, 1)', end: 'rgba(255, 240, 80, 1)', border: 'rgba(255, 250, 120, 0.9)' },
            { start: 'rgba(64, 224, 208, 1)', end: 'rgba(120, 255, 240, 1)', border: 'rgba(150, 255, 250, 0.9)' },
            { start: 'rgba(106, 90, 205, 1)', end: 'rgba(150, 140, 240, 1)', border: 'rgba(180, 170, 255, 0.9)' }
          ]
          
          const colorSet = colors[index % colors.length]
          
          // 根据当前任务类型获取对应数据
          let data = []
          if (this.taskType === 1) {
            data = taskData.submitData || []
          } else if (this.taskType === 2) {
            data = taskData.pendingData || []
          } else if (this.taskType === 3) {
            data = taskData.resourceData || []
          }
          
          series.push({
            name: `${taskData.taskId}-${taskData.strategy}`,
            type: 'bar',
            barWidth: fixedBarWidth, // 保持固定柱子宽度
            z: index + 1,
            label: {
              show: true,
              position: 'right',
              distance: 3, // 减少标签距离，为更多柱子腾出空间
              textStyle: {
                color: 'rgba(255, 255, 255, 0.95)',
                fontSize: Math.max(8, Math.min(11, 12 - Math.floor(taskCount / 3))), // 根据任务数量动态调整字体大小
                fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
              },
              formatter
            },
            emphasis: {
              focus: 'none', // 移除悬浮时隐藏其他柱子的效果
              itemStyle: {
                borderColor: colorSet.border,
                shadowColor: colorSet.border,
                shadowBlur: 8
              }
            },
            data: data,
            itemStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [ 
                { offset: 0, color: colorSet.start },
                { offset: 1, color: colorSet.end }
              ]),
              borderRadius: [0, 3, 3, 0], // 减少圆角，为细柱子优化
              borderColor: colorSet.border,
              borderWidth: 0.5
            }
          })
        })
      } else {
        console.log('进入兼容模式（单任务/双任务）')
        // 兼容原有的单任务/双任务模式
        const taskCount = this.compareId != 0 && this.taskData.used2 && this.taskData.used2.length > 0 ? 2 : 1
        const cityCount = this.taskData.xData ? this.taskData.xData.length : 0
        const fixedBarWidth = this.getFixedBarWidth(taskCount)
        
        series.push({
          name: this.taskId == 0 ? '' : this.taskId + '-' + this.Strategy1,
          type: 'bar',
          barWidth: fixedBarWidth, // 保持固定柱子宽度
          z: 1,
          label: {
            show: true,
            position: 'right',
            distance: 5,
            textStyle: {
              color: 'rgba(255, 255, 255, 0.95)',
              fontSize: 11,
              fontFamily: '思源黑体, Microsoft YaHei, sans-serif',
              // fontWeight: 'bold' // 可以考虑加粗
            },
            formatter
          },
          emphasis: {
            focus: 'none', // 移除悬浮时隐藏其他柱子的效果
            itemStyle: {
              borderColor: 'rgba(0, 230, 255, 1)',
              shadowColor: 'rgba(0, 230, 255, 0.8)',
              shadowBlur: 10
            }
          },
          data: this.taskData.used || [],
          itemStyle: {
            color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [ 
              { offset: 0, color: 'rgba(30, 144, 255, 1)' }, 
              { offset: 1, color: 'rgba(100, 200, 255, 1)' } 
            ]),
            borderRadius: [0, 5, 5, 0], // 右侧圆角
            borderColor: 'rgba(130, 220, 255, 0.9)',
            borderWidth: 1,
            shadowColor: 'rgba(80, 180, 255, 0.7)',
            shadowBlur: 5
          }
        })

        if (this.compareId != 0 && this.taskData.used2 && this.taskData.used2.length > 0) {
          series.push({
            name: this.compareId == 0 ? '' : this.compareId + '-' + this.Strategy2, // 使用短横线连接
            type: 'bar',
            barWidth: fixedBarWidth, // 保持固定柱子宽度
            z: 2,
            label: {
              show: true,
              position: 'right',
              distance: 5,
              textStyle: {
                color: 'rgba(255, 255, 255, 0.95)',
                fontSize: 11,
                fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
              },
              formatter
            },
            emphasis: {
              focus: 'none', // 移除悬浮时隐藏其他柱子的效果
              itemStyle: {
                borderColor: 'rgba(170, 190, 255, 1)',
                shadowColor: 'rgba(170, 190, 255, 0.8)',
                shadowBlur: 10
              }
            },
            data: this.taskData.used2 || [],
            itemStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [ 
                { offset: 0, color: 'rgba(255, 140, 0, 1)' }, 
                { offset: 1, color: 'rgba(255, 180, 80, 1)' } 
              ]),
              borderRadius: [0, 5, 5, 0],
              borderColor: 'rgba(255, 200, 120, 0.9)',
              borderWidth: 1,
              shadowColor: 'rgba(255, 160, 60, 0.7)',
              shadowBlur: 5
            }
          })
        } else if (this.compareId != 0) { // 如果有compareId但数据为空，也画一个空的系列占位，使得图例正确显示
          series.push({
              name: this.compareId + '-' + this.Strategy2, // 使用短横线连接
              type: 'bar',
              data: []
          })
        }
      }

      console.log('最终创建的系列数量:', series.length)
      console.log('系列详情:', series.map(s => ({ name: s.name, dataLength: s.data?.length, barWidth: s.barWidth })))
      return series
    }
  }
}
