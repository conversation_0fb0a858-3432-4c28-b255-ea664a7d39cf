/**
 * 数据处理模块
 * 负责处理任务数据、图表数据的转换和处理
 */

import { jobDetail } from '@/api/screenService.js'
import MultiTaskService from '@/services/MultiTaskService'

export default {
  methods: {
    /**
     * 重置图表和任务数据
     */
    resetChartData() {
      // 初始化为空数据，仅保持图表框架
      this.Data = {
        xAxis: [],
        yAxis: []
      }

      this.taskData = {
        xData: [],
        used: [],
        used2: [],
        multiTaskData: []
      }

      this.drawLine()
      this.drawLine2()
      this.count = 0
      this.buttonShow = true
    },

    /**
     * 重置主要信息和任务详情
     */
    resetTaskInfo() {
      this.msg = {
        SnapshotTime: 0,
        NCenters: 0,
        NPops: 0
      }
      this.msg2 = {
        totalNum: 0,
        execNum: 0
      }
      this.taskDetail = {
        ID: undefined,
        NJobs: undefined,
        SnapshotTime: undefined,
        CompletedFlag: undefined,
        strategy: undefined
      }
    },

    /**
     * 初始化任务类型数据对象
     */
    initTaskTypeData() {
      return {
        used: null,
        xData: [],
        used2: null
      }
    },

    /**
     * 处理多个任务的数据
     * @param {Array} tasksData - 多个任务的数据数组
     */
    processMultipleTasksData(tasksData) {
      console.log('开始处理多任务数据:', tasksData.length, '个任务')
      
      // 使用第一个任务作为主任务，折线图显示主任务的数据
      const mainTask = tasksData[0]
      
      // 设置基本任务信息（使用主任务的信息）
      this.initBaseTaskInfo(mainTask)
      
      // 使用主任务的数据设置折线图（与单任务模式保持一致）
      if (
        mainTask.CenterInfoToWebList != null &&
        mainTask.CenterInfoToWebList[0] &&
        mainTask.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      ) {
        this.total = mainTask.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      }

      const {
        historySubmitJob,
        historyCompleteJob,
        submitJob,
        completeJob,
        Time,
        city,
        submitJobCompare,
        pendingJobCompare,
        resouceCompare
      } = this.extractJobData(mainTask.CenterInfoToWebList)

      // 设置折线图数据（使用主任务的数据，与单任务模式一致）
      const xAxis = JSON.parse(JSON.stringify(Time))
      const yAxis = JSON.parse(JSON.stringify(submitJob))
      this.msg.SnapshotTime = Time[this.count]
      this.Data.xAxis = xAxis
      this.temp = JSON.parse(JSON.stringify(yAxis))
      this.Data.yAxis[this.count] = yAxis[this.count]

      this.runningTime = Time[this.count]
      
      // 初始化主任务的对比数据
      this.initMainCompareData(city, submitJobCompare, pendingJobCompare, resouceCompare)
      
      // 处理右侧多任务对比图表数据
      this.processMultipleTasksCompareData(tasksData)
      
      // 绘制图表
      this.drawLine()
      this.drawLine2()
      this.disable = false
      
      // 确保显示暂停/继续按钮
      this.showButtom = true
      
      // 启动动画定时器（使用主任务的数据）
      this.startMultiTaskAnimationTimer(Time, historySubmitJob, historyCompleteJob, city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used, xAxis, tasksData)
    },

    /**
     * 处理多任务对比数据
     * @param {Array} tasksData - 多个任务的数据数组
     */
    processMultipleTasksCompareData(tasksData) {
      console.log('processMultipleTasksCompareData 被调用，tasksData:', tasksData.length, '个任务')
      const compareData = MultiTaskService.getMultipleTasksCompareData(tasksData, this.count)
      console.log('MultiTaskService.getMultipleTasksCompareData 返回:', compareData)
      
      // 设置x轴数据（城市列表）
      this.taskData.xData = compareData.xData
      
      // 存储所有任务的数据
      this.taskData.multiTaskData = compareData.series.map(series => ({
        taskId: series.taskId,
        strategy: series.strategy,
        submitData: series.submitData,
        pendingData: series.pendingData,
        resourceData: series.resourceData
      }))
      
      console.log('设置后的 taskData.multiTaskData:', this.taskData.multiTaskData)
      
      // 兼容原有的双任务模式（保持向后兼容）
      if (compareData.series.length > 0) {
        const firstSeries = compareData.series[0]
        
        if (this.taskType === 1) {
          this.taskData.used = firstSeries.submitData
          if (compareData.series.length > 1) {
            this.taskData.used2 = compareData.series[1].submitData
          }
        } else if (this.taskType === 2) {
          this.taskData.used = firstSeries.pendingData
          if (compareData.series.length > 1) {
            this.taskData.used2 = compareData.series[1].pendingData
          }
        } else if (this.taskType === 3) {
          this.taskData.used = firstSeries.resourceData
          if (compareData.series.length > 1) {
            this.taskData.used2 = compareData.series[1].resourceData
          }
        }
      }
    },

    /**
     * 获取任务详情数据
     */
    getJobDetail(id) {
      this.showStart = false
      clearInterval(this.timer)
      this.timer = null
      const data = {
        id: id,
        resolution_n_hours: this.interval
      }
      this.disable = true
      this.showButtom = true
      this.showPer = true
      jobDetail(data).then((res) => {
        this.processMainJobData(res)
      })
    },

    /**
     * 获取对比任务数据
     */
    getCompare(id) {
      if (!id == 0) {
        const data = {
          id: id,
          resolution_n_hours: this.interval
        }
        this.disable = true
        this.showButtom = true
        this.showPer = true
        jobDetail(data).then((res) => {
          this.processCompareJobData(res)
        })
      } else {
        this.taskData.used2 = []
        this.CenterInfoToWebList = null
      }
    },

    /**
     * 处理主任务数据
     */
    processMainJobData(res) {
      this.initBaseTaskInfo(res)

      if (
        res.CenterInfoToWebList != null &&
        res.CenterInfoToWebList[0] &&
        res.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      ) {
        this.total = res.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      }

      const {
        historySubmitJob,
        historyCompleteJob,
        submitJob,
        completeJob,
        Time,
        city,
        submitJobCompare,
        pendingJobCompare,
        resouceCompare
      } = this.extractJobData(res.CenterInfoToWebList)

      this.initMainCompareData(city, submitJobCompare, pendingJobCompare, resouceCompare)

      const xAxis = JSON.parse(JSON.stringify(Time))
      const yAxis = JSON.parse(JSON.stringify(submitJob))
      this.msg.SnapshotTime = Time[this.count]
      this.Data.xAxis = xAxis
      this.temp = JSON.parse(JSON.stringify(yAxis))
      this.Data.yAxis[this.count] = yAxis[this.count]

      this.runningTime = Time[this.count]
      this.updateTaskDataByType(city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used)

      this.drawLine()
      this.drawLine2()
      this.disable = false

      // 确保显示暂停/继续按钮
      this.showButtom = true

      if (this.compareId !== 0) {
        this.getCompare(this.compareId)
      }

      this.startAnimationTimer(Time, historySubmitJob, historyCompleteJob, city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used, xAxis)
    },

    /**
     * 初始化基本任务信息
     */
    initBaseTaskInfo(res) {
      this.msg.SnapshotTime = 0
      this.msg.NPops = res.NPops
      this.msg.NCenters = res.NCenters
      this.taskDetail.SnapshotTime = res.SnapshotTime
      this.taskDetail.ID = res.ID
      this.taskDetail.strategy = res.Strategy
      this.taskDetail.NJobs = res.NJobs
      this.taskDetail.CompletedFlag = res.CompletedFlag
    },

    /**
     * 初始化主对比数据
     */
    initMainCompareData(city, submitJobCompare, pendingJobCompare, resouceCompare) {
      this.submitJobCompare0 = this.initTaskTypeData()
      this.pendingJobCompare0 = this.initTaskTypeData()
      this.resouceCompare0 = this.initTaskTypeData()

      this.submitJobCompare0.xData = city
      this.pendingJobCompare0.xData = city
      this.resouceCompare0.xData = city
      this.submitJobCompare0.used = submitJobCompare.used
      this.pendingJobCompare0.used = pendingJobCompare.used
      this.resouceCompare0.used = resouceCompare.used
    },

    /**
     * 处理对比任务数据
     */
    processCompareJobData(res) {
      this.CenterInfoToWebList = res.CenterInfoToWebList
      this.total2 = res.CenterInfoToWebList[0].SnapshotInfoToWebList.length

      this.submitJobCompare = this.initTaskTypeData()
      this.pendingJobCompare = this.initTaskTypeData()
      this.resouceCompare = this.initTaskTypeData()

      const {
        city,
        data1,
        data2,
        data3
      } = this.processCompareSnapshots(res)

      this.submitJobCompare.xData = city
      this.pendingJobCompare.xData = city
      this.resouceCompare.xData = city
      this.submitJobCompare.used2 = data1
      this.pendingJobCompare.used2 = data2
      this.resouceCompare.used2 = data3
      this.taskData.xData = this.submitJobCompare0.xData
    },

    /**
     * 处理对比任务的快照数据
     */
    processCompareSnapshots(res) {
      const city = []
      const data1 = []
      const data2 = []
      const data3 = []
      this.disable = false

      res.CenterInfoToWebList.forEach((item) => {
        city.push(item.InfoName)
      })

      if (this.total2 <= this.total) {
        this.CenterInfoToWebList.forEach((item) => {
          item.SnapshotInfoToWebList.length = this.total
        })
      }

      for (let i = 0; i < this.total; i++) {
        data1[i] = []
        data2[i] = []
        data3[i] = []
        res.CenterInfoToWebList.forEach((item, key) => {
          const HistorySubmitJob = item.SnapshotInfoToWebList[this.total2 - 1].HistorySubmitJob
          const AveragePendingTime = item.SnapshotInfoToWebList[this.total2 - 1].AveragePendingTime
          const AverageMachineUse = item.SnapshotInfoToWebList[this.total2 - 1].AverageMachineUse

          data1[i][key] = item.SnapshotInfoToWebList[i] != undefined
            ? item.SnapshotInfoToWebList[i].HistorySubmitJob
            : HistorySubmitJob
          data2[i][key] = item.SnapshotInfoToWebList[i] != undefined
            ? item.SnapshotInfoToWebList[i].AveragePendingTime
            : AveragePendingTime
          data3[i][key] = item.SnapshotInfoToWebList[i] != undefined
            ? item.SnapshotInfoToWebList[i].AverageMachineUse
            : AverageMachineUse
        })
      }

      return {
        city,
        data1,
        data2,
        data3
      }
    },

    /**
     * 从CenterInfoToWebList中提取各种任务数据的通用方法
     */
    extractJobData(centerInfoList) {
      const historySubmitJob = []
      const historyCompleteJob = []
      const submitJob = []
      const completeJob = []
      const Time = []
      this.Data.xAxis = []
      this.Data.yAxis = []

      const submitJobCompare = this.initTaskTypeData()
      const pendingJobCompare = this.initTaskTypeData()
      const resouceCompare = this.initTaskTypeData()

      const city = []
      const data1 = []
      const data2 = []
      const data3 = []

      centerInfoList.forEach((item) => {
        city.push(item.InfoName)
      })

      for (let i = 0; i < this.total; i++) {
        let HistorySubmitJob = 0
        let HistoryCompleteJob = 0
        let SubmitJob = 0
        let CompleteJob = 0
        data1[i] = []
        data2[i] = []
        data3[i] = []

        centerInfoList.forEach((item, key) => {
          if (item.SnapshotInfoToWebList[i]) {
            HistorySubmitJob += item.SnapshotInfoToWebList[i].HistorySubmitJob
            HistoryCompleteJob += item.SnapshotInfoToWebList[i].HistoryCompleteJob
            SubmitJob += item.SnapshotInfoToWebList[i].SubmitJob
            CompleteJob += item.SnapshotInfoToWebList[i].CompleteJob
            data1[i][key] = item.SnapshotInfoToWebList[i].HistorySubmitJob
            data2[i][key] = item.SnapshotInfoToWebList[i].AveragePendingTime
            data3[i][key] = item.SnapshotInfoToWebList[i].AverageMachineUse
          }
        })

        historySubmitJob[i] = HistorySubmitJob
        historyCompleteJob[i] = HistoryCompleteJob
        submitJob[i] = SubmitJob
        completeJob[i] = CompleteJob
      }

      submitJobCompare.xData = JSON.parse(JSON.stringify(city))
      pendingJobCompare.xData = JSON.parse(JSON.stringify(city))
      resouceCompare.xData = JSON.parse(JSON.stringify(city))
      submitJobCompare.used = data1
      pendingJobCompare.used = data2
      resouceCompare.used = data3

      centerInfoList[0].SnapshotInfoToWebList.forEach((item) => {
        // 使用修改后的formatDuring确保返回纯小时数
        const timeValue = this.formatDuring(item.Time)
        Time.push(timeValue)
      })

      return {
        historySubmitJob,
        historyCompleteJob,
        submitJob,
        completeJob,
        Time,
        city,
        submitJobCompare,
        pendingJobCompare,
        resouceCompare
      }
    },

    /**
     * 根据任务类型更新任务数据的通用方法
     */
    updateTaskDataByType(city, submitJobUsed, pendingJobUsed, resouceCompareUsed, index) {
      const idx = index !== undefined ? index : this.count

      if (this.taskType == 1) {
        this.updateTaskDataForType(city, submitJobUsed[idx], this.submitJobCompare?.used2?.[idx])
      }
      if (this.taskType == 2) {
        this.updateTaskDataForType(city, pendingJobUsed[idx], this.pendingJobCompare?.used2?.[idx])
      }
      if (this.taskType == 3) {
        this.updateTaskDataForType(city, resouceCompareUsed[idx], this.resouceCompare?.used2?.[idx])
      }
    },

    /**
     * 更新指定类型的任务数据
     */
    updateTaskDataForType(city, usedData, usedData2) {
      this.taskData.xData = city
      this.taskData.used = usedData
      if (usedData2) {
        this.taskData.used2 = usedData2
      }
    },

    /**
     * 获取任务数据
     */
    async getJobData() {
      const promises = []
      this.taskIdRes = null
      this.compareIdRes = null

      if (this.taskId !== 0) {
        const data1 = {
          id: this.taskId,
          resolution_n_hours: this.interval
        }
        promises.push(
          jobDetail(data1).then(res => {
            this.taskIdRes = res
          })
        )
      }

      if (this.compareId !== 0) {
        const data2 = {
          id: this.compareId,
          resolution_n_hours: this.interval
        }
        promises.push(
          jobDetail(data2).then(res => {
            this.compareIdRes = res
          })
        )
      }

      await Promise.all(promises)
      return {
        taskIdRes: this.taskIdRes,
        compareIdRes: this.compareIdRes
      }
    },

    /**
     * 处理任务数据
     */
    processJobData(taskRes, compareRes) {
      this.prepareForProcessing()

      if (!taskRes) return

      // 处理主任务数据
      this.initBaseTaskInfo(taskRes)

      if (
        taskRes.CenterInfoToWebList != null &&
        taskRes.CenterInfoToWebList[0] &&
        taskRes.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      ) {
        this.total = taskRes.CenterInfoToWebList[0].SnapshotInfoToWebList.length
      }

      const {
        historySubmitJob,
        historyCompleteJob,
        submitJob,
        completeJob,
        Time,
        city,
        submitJobCompare,
        pendingJobCompare,
        resouceCompare
      } = this.extractJobData(taskRes.CenterInfoToWebList)

      this.initMainCompareData(city, submitJobCompare, pendingJobCompare, resouceCompare)

      const xAxis = JSON.parse(JSON.stringify(Time))
      const yAxis = JSON.parse(JSON.stringify(submitJob))
      this.msg.SnapshotTime = Time[this.count]
      this.Data.xAxis = xAxis
      this.temp = JSON.parse(JSON.stringify(yAxis))
      this.Data.yAxis[this.count] = yAxis[this.count]

      this.runningTime = Time[this.count]
      this.updateTaskDataByType(city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used)

      // 处理对比任务数据
      if (compareRes) {
        this.processCompareData(compareRes, city)
      }

      this.finalizeProcessing()
      this.startAnimationTimer(Time, historySubmitJob, historyCompleteJob, city, submitJobCompare.used, pendingJobCompare.used, resouceCompare.used, xAxis)
    },

    /**
     * 准备处理数据
     */
    prepareForProcessing() {
      this.showStart = false
      clearInterval(this.timer)
      this.timer = null
      this.disable = true
      this.showButtom = true
      this.showPer = true
    },

    /**
     * 完成数据处理
     */
    finalizeProcessing() {
      this.drawLine()
      this.drawLine2()
      this.disable = false
    },

    /**
     * 处理对比数据
     */
    processCompareData(compareRes, city) {
      if (!compareRes) return

      this.CenterInfoToWebList = compareRes.CenterInfoToWebList
      this.total2 = compareRes.CenterInfoToWebList[0].SnapshotInfoToWebList.length

      this.submitJobCompare = this.initTaskTypeData()
      this.pendingJobCompare = this.initTaskTypeData()
      this.resouceCompare = this.initTaskTypeData()

      const {
        city: compareCity,
        data1,
        data2,
        data3
      } = this.processCompareSnapshots(compareRes)

      this.submitJobCompare.xData = compareCity
      this.pendingJobCompare.xData = compareCity
      this.resouceCompare.xData = compareCity
      this.submitJobCompare.used2 = data1
      this.pendingJobCompare.used2 = data2
      this.resouceCompare.used2 = data3
      this.taskData.xData = city
    },

    /**
     * 初始化空图表数据结构
     * @returns {Object} 包含初始化后的图表数据结构
     */
    initEmptyChartData() {
      return {
        // 折线图数据
        chartData: {
          xAxis: [],
          yAxis: []
        },
        // 柱状图数据
        barData: {
          xData: [],
          used: [],
          used2: [],
          multiTaskData: []
        },
        // 任务信息
        taskInfo: {
          SnapshotTime: 0,
          NCenters: 0,
          NPops: 0
        },
        // 任务统计
        taskStats: {
          totalNum: 0,
          execNum: 0
        },
        // 任务详情
        taskDetail: {
          ID: undefined,
          NJobs: undefined,
          SnapshotTime: undefined,
          CompletedFlag: undefined,
          strategy: undefined
        }
      }
    },

    /**
     * 重置所有图表数据
     */
    resetAllChartData() {
      const emptyData = this.initEmptyChartData()

      // 重置各数据结构
      this.Data = emptyData.chartData
      this.taskData = emptyData.barData
      this.msg = emptyData.taskInfo
      this.msg2 = emptyData.taskStats
      this.taskDetail = emptyData.taskDetail

      // 重绘图表
      this.drawLine()
      this.drawLine2()

      // 重置状态
      this.count = 0
      this.buttonShow = true
      clearInterval(this.timer)
    }
  }
}
