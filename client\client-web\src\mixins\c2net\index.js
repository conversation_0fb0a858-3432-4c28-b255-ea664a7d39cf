/**
 * C2Net 主 mixin 文件
 * 整合所有功能模块
 */

import dataProcessing from './dataProcessing'
import chartConfig from './chartConfig'
import barSeriesConfig from './barSeriesConfig'
import taskManagement from './taskManagement'
import animationControl from './animationControl'
import utils from './utils'

export default {
  mixins: [
    dataProcessing,
    chartConfig,
    barSeriesConfig,
    taskManagement,
    animationControl,
    utils
  ],

  data() {
    return {
      // 对话框相关状态
      addTaskDialogVisible: false,
      addCooperativeTaskDialogVisible: false,
      taskDetailDialogVisible: false,
      currentTaskId: '',
      currentTaskIsCooperative: false, // 新增：标识当前任务是否为协同任务
      currentTaskData: null, // 新增：存储当前任务的详细数据
      
      // 图表数据
      taskData: {
        xData: [],
        used: [],
        used2: [],
        // 新增：支持多个任务的数据数组
        multiTaskData: [] // 格式: [{taskId, strategy, submitData, pendingData, resourceData}, ...]
      },
      Data: {
        xAxis: [],
        yAxis: []
      },
      
      // 任务状态信息
      msg: {
        SnapshotTime: 0,
        NCenters: 0,
        NPops: 0
      },
      msg2: {
        totalNum: 0,
        execNum: 0
      },
      taskDetail: {
        ID: undefined,
        NJobs: undefined,
        SnapshotTime: undefined,
        CompletedFlag: undefined,
        strategy: undefined
      },
      
      // 任务对比数据
      submitJobCompare0: null,
      pendingJobCompare0: null,
      resouceCompare0: null,
      submitJobCompare: null,
      pendingJobCompare: null,
      resouceCompare: null,
      
      // 计时和控制相关
      timer: null,
      count: 0,
      nowCount: 0,
      total: 0,
      total2: 0,
      runningTime: 0,
      
      // 图表和任务类型
      check1: true,
      check2: false,
      check3: false,
      taskType: 1,
      name: '提交任务量',
      myChart: null,
      myChart2: null,
      
      // UI控制状态
      buttonShow: true,
      disable: false,
      Stop: true,
      showButtom: true,
      showPer: true,
      intervalChange: true,
      showStart: false,
      
      // 时间配置
      options: [
        { value: 24, label: '24h' },
        { value: 12, label: '12h' },
        { value: 6, label: '6h' }
      ],
      value: 24,
      percentage: 0,
      
      // 数据缓存
      temp: null,
      taskIdRes: null,
      compareIdRes: null,
      CenterInfoToWebList: null,
      dataAlreadyLoaded: false,
      
      // 地图相关
      show2: false,
      show3: false,
      tipData: {}
    }
  },

  computed: {
    taskId() {
      return this.$store.state.id
    },
    compareId() {
      return this.$store.state.compareId
    },
    Strategy1() {
      return this.$store.state.strategy1
    },
    Strategy2() {
      return this.$store.state.strategy2
    },
    lastTime() {
      return this.$store.state.lastTime
    },
    interval() {
      return this.$store.state.interval
    },
    // 新增：多任务相关的computed属性
    selectedTasks() {
      return this.$store.state.selectedTasks
    },
    selectedTaskIds() {
      return this.$store.state.selectedTaskIds
    }
  },

  watch: {
    taskId(newValue) {
      // 保持地图状态
      if (this.$refs.chinaMap) {
        this.$refs.chinaMap.stopped = false

        // 如果地图上有数据，保留它们
        if (this.$refs.chinaMap.nowConfig &&
          this.$refs.chinaMap.nowConfig.points &&
          this.$refs.chinaMap.nowConfig.points.length > 0) {
          // 地图数据已存在，不清空
        } else {
          // 如果没有数据，初始化演示数据
          this.$refs.chinaMap.initDemoData()
          this.$refs.chinaMap.showDot()
        }
      }

      // 重置数据
      this.$nextTick(() => {
        this.resetAllChartData()
        this.intervalChange = this.taskId !== 0
      })
    },
    
    compareId() {
      // 保持地图状态
      if (this.$refs.chinaMap) {
        this.$refs.chinaMap.stopped = false
      }

      // 重置图表数据
      this.$nextTick(() => {
        this.resetAllChartData()
      })
    },
    
    count(newValue) {
      // 更新进度百分比
      if (this.count == this.total) {
        this.percentage = 100
      } else if (this.count == 0) {
        this.percentage = 0
      } else {
        this.percentage = Number(((this.count / this.total) * 100).toFixed(0))
      }
      
      // 更新数据和状态
      if (newValue == this.total) {
        this.Data.yAxis = this.temp
        this.drawLine()
        clearInterval(this.timer)
        this.percentage = 100
        this.Stop = true
      } else {
        // 更新折线图数据
        this.temp.forEach((item, key) => {
          if (key <= this.count + 1) {
            this.Data.yAxis[key] = this.temp[key]
          } else {
            this.Data.yAxis[key] = ''
          }
        })
      }
    }
  },

  mounted() {
    // 初始化图表
    this.$nextTick(() => {
      // 初始化图表实例
      if (this.myChart) {
        this.myChart.dispose()
      }
      if (this.myChart2) {
        this.myChart2.dispose()
      }

      this.myChart = this.$echarts.init(this.$refs.echart)
      this.myChart2 = this.$echarts.init(this.$refs.echart2)

      // 初始化空数据
      this.resetAllChartData()

      // 添加窗口大小调整监听
      window.addEventListener('resize', this.resize)
    })
  },

  beforeDestroy() {
    // 清理资源
    if (this.myChart) {
      this.myChart.dispose()
    }
    if (this.myChart2) {
      this.myChart2.dispose()
    }
    window.removeEventListener('resize', this.resize)
    clearInterval(this.timer)
    this.timer = null
  }
}
