/**
 * 任务管理模块
 * 负责任务的创建、详情查看、提交等操作
 */

import { loadTaskYaml } from '@/api/yamlService'

export default {
  methods: {
    /**
     * 打开新增任务对话框
     */
    openAddTaskDialog() {
      this.addTaskDialogVisible = true
    },

    /**
     * 关闭新增任务对话框
     */
    handleCloseTaskDialog() {
      this.addTaskDialogVisible = false
    },

    /**
     * 打开新增协同任务对话框
     */
    openAddCooperativeTaskDialog() {
      this.addCooperativeTaskDialogVisible = true
    },

    /**
     * 关闭新增协同任务对话框
     */
    handleCloseCooperativeTaskDialog() {
      this.addCooperativeTaskDialogVisible = false
    },

    /**
     * 提交任务表单
     */
    submitTaskForm(responseOrFormData) {
      console.log('submitTaskForm被调用，参数:', responseOrFormData)

      // 判断参数是否为generateYaml API的响应
      if (responseOrFormData && (responseOrFormData.yaml_content || responseOrFormData.filename)) {
        // 如果是来自TaskDialog的generateYaml响应
        this.$message.success('任务创建成功')
        this.addTaskDialogVisible = false

        // 延迟1秒后刷新任务列表
        setTimeout(() => {
          console.log('新增任务成功后刷新任务列表')
          if (this.$refs.table) {
            console.log('调用table.init()刷新任务列表')
            this.$refs.table.init()
          } else {
            console.error('table引用不存在，无法刷新任务列表')
          }
        }, 1000)
      } else {
        // 如果是表单数据，执行原来的逻辑
        this.$http.post('/api/v1/tasks', responseOrFormData)
          .then(response => {
            this.$message.success('任务创建成功')
            this.addTaskDialogVisible = false
            // 延迟1秒后刷新任务列表
            setTimeout(() => {
              console.log('新增任务成功后刷新任务列表')
              if (this.$refs.table) {
                console.log('调用table.init()刷新任务列表')
                this.$refs.table.init()
              } else {
                console.error('table引用不存在，无法刷新任务列表')
              }
            }, 1000)
          })
          .catch(error => {
            this.$message.error('创建任务失败: ' + error.message)
          })
      }
    },

    /**
     * 提交协同任务表单
     */
    submitCooperativeTaskForm(responseOrFormData) {
      console.log('submitCooperativeTaskForm被调用，参数:', responseOrFormData)

      // 判断参数是否为generateYaml API的响应
      if (responseOrFormData && (responseOrFormData.yaml_content || responseOrFormData.filename)) {
        // 如果是来自CooperativeTaskDialog的generateYaml响应
        this.$message.success('协同任务创建成功')
        this.addCooperativeTaskDialogVisible = false

        // 延迟1秒后刷新任务列表
        setTimeout(() => {
          console.log('新增协同任务成功后刷新任务列表')
          if (this.$refs.table) {
            console.log('调用table.init()刷新任务列表')
            this.$refs.table.init()
          } else {
            console.error('table引用不存在，无法刷新任务列表')
          }
        }, 1000)
      } else {
        // 如果是表单数据，执行原来的逻辑
        this.$http.post('/api/v1/cooperative-tasks', responseOrFormData)
          .then(response => {
            this.$message.success('协同任务创建成功')
            this.addCooperativeTaskDialogVisible = false
            // 延迟1秒后刷新任务列表
            setTimeout(() => {
              console.log('新增协同任务成功后刷新任务列表')
              if (this.$refs.table) {
                console.log('调用table.init()刷新任务列表')
                this.$refs.table.init()
              } else {
                console.error('table引用不存在，无法刷新任务列表')
              }
            }, 1000)
          })
          .catch(error => {
            this.$message.error('创建协同任务失败: ' + error.message)
          })
      }
    },

    /**
     * 打开任务详情弹窗
     */
    openTaskDetailDialog(row) {
      console.log('打开任务详情', row)
      this.currentTaskId = row.ID
      
      // 通过API获取任务配置，判断是否为协同任务
      console.log('正在请求任务配置以判断任务类型...')
      
      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在加载任务信息...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      loadTaskYaml(this.currentTaskId)
        .then(response => {
          console.log('获取任务配置成功:', response)
          
          // 根据配置内容判断是否为协同任务
          const config = response.yaml_data || {}
          
          // 判断标准：根据c2netJobConfig.useJsonFile字段
          // useJsonFile为false时是协同任务，为true或不存在时是普通任务
          let isCooperativeTask = false
          if (config.c2netJobConfig && config.c2netJobConfig.hasOwnProperty('useJsonFile')) {
            isCooperativeTask = (config.c2netJobConfig.useJsonFile === false)
          } else {
            // 如果没有useJsonFile字段，则根据其他特征判断（降级逻辑）
            isCooperativeTask = !!(
              config.c2netComputingCenterList ||
              (config.c2netJobConfig && config.c2netJobConfig.c2netJobGroupList) ||
              config.scheduleConfig ||
              (config.networkConfig && (config.networkConfig.construct || config.networkConfig.accelerator))
            )
          }
          
          this.currentTaskIsCooperative = isCooperativeTask
          // 将完整的数据存储到currentTaskData中，供弹窗组件使用
          this.currentTaskData = {
            taskId: this.currentTaskId,
            taskInfo: response.task || {},
            yamlData: config,
            rawResponse: response
          }
          console.log('任务类型判断结果:', {
            taskId: this.currentTaskId,
            isCooperativeTask,
            useJsonFile: config.c2netJobConfig?.useJsonFile,
            hasUseJsonFile: config.c2netJobConfig?.hasOwnProperty('useJsonFile'),
            configStructure: {
              hasC2netJobConfig: !!config.c2netJobConfig,
              hasComputingCenterList: !!config.c2netComputingCenterList,
              hasScheduleConfig: !!config.scheduleConfig,
              hasNetworkConfig: !!config.networkConfig
            }
          })
          
          // 确保DOM更新后再显示弹窗
          this.$nextTick(() => {
            this.taskDetailDialogVisible = true
            console.log('当前任务ID:', this.currentTaskId, '协同任务:', this.currentTaskIsCooperative, '对话框可见性:', this.taskDetailDialogVisible)
          })
        })
        .catch(error => {
          console.error('获取任务配置失败:', error)
          
          // 如果API请求失败，降级使用row中的数据（如果存在）
          if (row.hasOwnProperty('useJsonFile')) {
            console.log('API请求失败，降级使用row数据判断任务类型')
            this.currentTaskIsCooperative = row.useJsonFile === true || row.useJsonFile === 1
            console.log('降级判断结果 - 协同任务:', this.currentTaskIsCooperative)
          } else {
            // 默认当作普通任务处理
            console.log('无法判断任务类型，默认作为普通任务处理')
            this.currentTaskIsCooperative = false
          }
          
          // 确保DOM更新后再显示弹窗
          this.$nextTick(() => {
            this.taskDetailDialogVisible = true
          })
        })
        .finally(() => {
          loading.close()
        })
    },

    /**
     * 处理任务详情弹窗关闭
     */
    handleTaskDetailClose() {
      this.taskDetailDialogVisible = false
      // 清空任务数据
      this.currentTaskData = null
      this.currentTaskId = ''
      this.currentTaskIsCooperative = false
    },

    /**
     * 处理任务提交成功
     */
    handleTaskSubmitSuccess() {
      console.log('任务提交/重新提交成功事件被触发')
      // 刷新任务列表
      if (this.$refs.table) {
        console.log('调用table.init()刷新任务列表')
        this.$refs.table.init()
      } else {
        console.error('table引用不存在，无法刷新任务列表')
      }
    },

    /**
     * 切换任务类型
     */
    change(val) {
      this.taskType = val
      this.taskData = {
        xData: this.taskData.xData || [],
        used: [],
        used2: [],
        multiTaskData: this.taskData.multiTaskData || []
      }

      this.updateUIByTaskType(val)
      this.drawLine2()
    },

    /**
     * 根据任务类型更新UI
     */
    updateUIByTaskType(type) {
      const isAtEnd = this.count == this.total
      const currentIndex = isAtEnd ? this.count - 1 : this.count

      // 更新选中状态
      this.check1 = type == 1
      this.check2 = type == 2
      this.check3 = type == 3

      // 设置任务名称
      const nameMap = {
        1: '提交任务量',
        2: '任务平均等待时长',
        3: '资源利用率对比'
      }
      this.name = nameMap[type]

      // 更新任务数据
      // 如果有多任务数据，优先使用多任务数据
      if (this.taskData.multiTaskData && this.taskData.multiTaskData.length > 0) {
        // 多任务模式：数据已经在multiTaskData中，不需要额外处理
        // createBarSeriesConfig会直接从multiTaskData中获取数据
      } else {
        // 兼容原有的双任务模式
        if (type == 1) {
          this.updateTaskDataForType(
            this.resouceCompare0?.xData || [],
            this.submitJobCompare0?.used?.[currentIndex],
            this.compareId !== 0 ? this.submitJobCompare?.used2?.[currentIndex] : undefined
          )
        } else if (type == 2) {
          this.updateTaskDataForType(
            this.pendingJobCompare0?.xData || [],
            this.pendingJobCompare0?.used?.[currentIndex],
            this.compareId !== 0 ? this.pendingJobCompare?.used2?.[currentIndex] : undefined
          )
        } else if (type == 3) {
          this.updateTaskDataForType(
            this.resouceCompare0?.xData || [],
            this.resouceCompare0?.used?.[currentIndex],
            this.compareId !== 0 ? this.resouceCompare?.used2?.[currentIndex] : undefined
          )
        }
      }
    }
  }
}
