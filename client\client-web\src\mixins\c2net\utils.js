/**
 * 工具函数模块
 * 包含各种通用的工具函数
 */

export default {
  methods: {
    /**
     * 格式化时间（返回小时数）
     */
    formatDuring(val) {
      // 修改为只返回小时数，不使用天(d)为单位
      const totalHours = Math.floor(val * 1000 / (1000 * 60 * 60))
      return totalHours
    },

    /**
     * 格式化时间2（返回小时数）
     */
    formatDuring2(val) {
      // 确保只返回小时数
      var hours = Math.floor(val * 3600 / 3600)
      return hours
    },

    /**
     * 格式化时间3（带单位）
     */
    formatDuring3(val) {
      // 修改为只返回小时为单位的时间
      if (val < (1 / 3600)) {
        return (val * 3600).toFixed(2) + 's'
      }

      // 转换为小时，包括小数部分
      const totalHours = val
      return totalHours.toFixed(2) + 'h'
    }
  },

  computed: {
    /**
     * 动态计算图表容器样式
     */
    chartContainerStyle() {
      return {
        width: '100%',
        height: '700px', // 固定容器高度
        maxHeight: '700px',
        overflowY: 'auto', // 添加垂直滚动条
        overflowX: 'hidden' // 隐藏水平滚动条
      }
    },

    /**
     * 计算图表实际高度
     */
    chartHeight() {
      const cityCount = this.taskData.xData ? this.taskData.xData.length : 0
      const taskCount = this.taskData.multiTaskData && this.taskData.multiTaskData.length > 0 
        ? this.taskData.multiTaskData.length 
        : (this.compareId != 0 && this.taskData.used2 && this.taskData.used2.length > 0 ? 2 : 1)
      
      return this.calculateChartHeight(cityCount, taskCount)
    }
  }
}
