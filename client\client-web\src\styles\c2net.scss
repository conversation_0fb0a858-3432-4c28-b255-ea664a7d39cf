.coopera-pic {
  margin: 0px 220px 0px 220px;
  background-image: url("../static/screen1/cooperativeUnit.jpg");
  background-repeat: no-repeat;
  background-size: 105%;
  height: 185px;
  width: 100%;
  margin-top: -150px;
  /* background-position-x: 290px; */
}

.el-header {
  height: 125px !important;
  background-image: url("../static/screen1/header.png");
  background-size: 100%;
}

.background {
  background-image: url("../static/screen1/background.png");
  background-size: 100% 100%;
  min-width: 2020px;
  min-height: 100vh;
  width: 100%;
}

.title {
  margin-top: 20px;
  font-size: 3rem;
  color: rgb(255, 255, 255);
  letter-spacing: 5px;
  line-height: 48px;
  text-align: center;
  width: 100%;
  font-weight: 800;
  /* transform: translateY(-50%); */
  filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
  text-align: center;
  margin-left: 20px;
}

/* .subTitle {
    margin-top: 1px;
    font-family: "思源黑体 CN-Regular";
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    letter-spacing: 0px;
    line-height: 20px;
    text-align: center;
    width: 100%;
    font-style: italic;
    font-weight: normal;
    filter: drop-shadow(rgb(0, 117, 255) 0px 0px 8px);
  } */

.subTitle span {
  letter-spacing: 1px;
}

.title1 {
  color: rgb(255, 255, 255);
  font-size: 20px;
  text-align: left;
  /* box-shadow: 0px 2px 6px 0px rgba(217, 235, 255, 0.64); */
  font-family: SourceHanSansSC-medium;
}

.province {
  width: 100px;
  height: 100px;
}

.arrow {
  background-size: 100% 100%;
  background-image: url("../static/screen1/arrow2.png");
  width: 50px;
  height: 35px;
  position: relative;
  top: -5px;
  background-repeat: no-repeat;
}

.linear-gradient {
  background: linear-gradient(
    to right,
    rgba(255, 0, 0, 0),
    rgba(255, 255, 255, 0.2)
  );
  margin: 6px 0 6px 0;
  box-sizing: content-box;
  background-clip: content-box;
}

.el-main {
  /* height: 900px; */
  padding-bottom: 0px;
  z-index: 20;
  overflow: hidden;
  /* padding: 10px; */
  /* padding-top: 30px; */
  padding: 0px 20px 20px 20px !important;
}

.el-container.is-vertical {
  height: 100%;
}

.showMt {
  margin-top: 0px !important;
  width: 570px;
}

.buttonStyle {
  height: 50px;
  color: white;
  line-height: 50px;
  text-align: center;
  background-color: rgba(255, 255, 255, 10%);
  cursor: pointer;
  min-width: 200px;
  font-weight: 800;
}

.check {
  font-weight: 800;
  background-image: linear-gradient(
    rgba(30, 231, 231, 40%),
    rgba(255, 255, 255, 10%),
    rgba(255, 255, 255, 0%)
  );
}

.footer {
  background-image: url("../assets/footer.svg");
  background-repeat: no-repeat;
  /* min-height: 20px !important; */
  background-size: 100% 100%;
  width: 100%;
  height: 90px;
}

.wrapper {
  position: absolute;
  left: 25px;
}

.icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-right: 10px;
  line-height: 18px;
}

.icon2 {
  display: inline-block;
  width: 25px;
  height: 25px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-right: 10px;
  line-height: 18px;
}

.text {
  display: inline-block;
  line-height: 18px;
  height: 18px;
  position: relative;
  top: -2px;
  margin-right: 2px;
}

.img1 {
  background-image: url("../static/screen1/intellectual.svg");
}

.img2 {
  background-image: url("../static/screen1/super.svg");
}

.img3 {
  background-image: url("../static/screen1/eandw.svg");
}

.img4 {
  background-image: url("../assets/statusEd.svg");
}

.img5 {
  background-image: url("../assets/statusIng.svg");
}

.img6 {
  background-image: url("../assets/statusUn.svg");
}

.buttonStyle2 {
  /* height: 50px; */
  color: white;
  /* line-height: 50px; */
  /* text-align: center; */
  /* background-color: rgba(255, 255, 255, 10%); */
  cursor: pointer;
  min-width: 150px;
  margin-bottom: 10px;
}

.check2 {
  color: #fff !important;
  background-image: linear-gradient(
    to right,
    rgba(24, 144, 255, 0%),
    rgba(24, 144, 255, 10%),
    rgba(24, 144, 255, 40%)
  );
}

.statusWrapper {
  position: absolute;
  right: 220px;
  top: 560px;
}

.statusWrapper2 {
  position: absolute;
  right: 220px;
  top: 650px;
}

.title2 {
  color: rgba(255, 255, 255, 0.7);
  line-height: 40px;
}

.title3 {
  line-height: 40px;
  color: rgba(255, 255, 255, 0.7);
  width: 140px;
}

.arrow1 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../assets/arrow10TB.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.arrow2 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../assets/arrowSD.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.arrow3 {
  color: rgba(255, 255, 255, 0.7);
  width: 220px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 400;
  font-family: SourceHanSansSC;
  background-image: url("../assets/arrowMPLS.svg");
  background-repeat: no-repeat;
  background-position-y: 5px;
}

.mt-2 {
  margin-top: 20px;
}

.ml-2 {
  margin-left: 65px;
}

.smalltitle {
  letter-spacing: 0px;
  font-size: 2.6rem;
}

.dropdown {
  width: 225px;
  position: fixed;
  right: 30px;
  top: 20px;
  z-index: 200;
}

.passwordPos {
  margin-top: 40vh;
}

.passConfirm {
  text-align: center;
}

.loading {
  position: relative;
  top: 250px;
}

.loading-text {
  color: #fff;
  font-weight: 800;
}

.taskDetailWrapper {
  /* width: 550px;
  height: 280px; */
  /* background-color: #0A1E37; */
  background-color: rgba(10, 30, 55, 0.25);
  background-size: cover;
  background-position: center center;
  /* overflow: hidden;
  filter: unset; */
  padding: 15px;
  position: absolute;
  right: 0px;
  top: 150px;
  min-height: 820px;
  border: 1px solid rgba(0, 190, 255, 0.4); /* 调整边框颜色 */
  width: 425px;
  box-shadow: inset 0 0 20px rgba(18, 137, 221, 0.2); /* 增加内阴影 */
  border-radius: 6px; /* 轻微圆角 */
}

.button {
  color: rgb(255, 255, 255);
  font-family: 思源黑体;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
}

.mt {
  margin-top: 10px; /* 调整上边距 */
  margin-bottom: 20px; /* 调整下边距 */
}

.loading {
  width: 100%;
  height: 100%;
  position: absolute;
  /* top: 0px; */
  z-index: 200;
}

.dv-loading {
  position: relative;
  top: -20%;
}

/* .check {
  color: rgb(50, 197, 255);
  font-family: 思源黑体;
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 0px;
} */
.check3 {
  color: #FFFFFF !important; /* Changed for selected text */
  font-family: 思源黑体;
  font-size: 15px;
  font-weight: 700;
  letter-spacing: 0;
  text-shadow: 0 0 7px rgba(0, 180, 255, 0.7); /* Adjusted glow for selected text */
}

.button2 {
  color: #CFD8DC; /* Changed for non-selected text */
  font-family: 思源黑体;
  font-size: 15px;
  text-align: center;
  cursor: pointer;
  font-weight: 700;
  display: block;
  width: 100%;
  padding: 12px 5px;
  text-decoration: none;
  transition: color 0.2s ease-in-out, text-shadow 0.2s ease-in-out; /* Updated transition */
  white-space: nowrap; /* Kept for single line */
  /* overflow: hidden; */ /* Removed */
  /* text-overflow: ellipsis; */ /* Removed */
}

.bg2 {
  box-sizing: border-box;
  background: linear-gradient(to bottom, #303E4D, #242F3A); /* New background */
  border: 1px solid rgba(0, 140, 190, 0.3); /* New border */
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.25); /* New box-shadow */
  padding: 0;
  text-align: center;
  margin-bottom: 2px;
  border-radius: 3px; /* New border-radius */
  transition: background 0.2s ease-in-out, border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out; /* New transition */
  cursor: pointer;
  /* transform: none; */ /* Ensure no transform */
}

.bg2:hover {
  background: linear-gradient(to bottom, #38495A, #293643); /* New hover background */
  border-color: rgba(0, 170, 220, 0.5); /* New hover border-color */
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 5px rgba(0, 170, 220, 0.2); /* New hover box-shadow */
  /* transform: none; */ /* Ensure no transform */
}

.bg2:active {
  background: linear-gradient(to bottom, #25303D, #1E2730); /* New active background */
  box-shadow: inset 0 2px 3px rgba(0, 0, 0, 0.35); /* New active box-shadow */
  /* transform: none; */ /* Ensure no transform */
  border-color: rgba(0, 140, 190, 0.2); /* Slightly subdued border on active */
}

.bg2:hover .button2 {
  color: #FFFFFF; /* New hover text color */
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.3); /* New hover text-shadow */
}

/* Selected tab styles */
.bg2.active-tab {
  background: linear-gradient(to bottom, #1A4E6E, #10354F); /* New selected background */
  border-color: #00AEEF; /* New selected border-color */
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2), 0 0 10px rgba(0, 174, 239, 0.4); /* New selected box-shadow */
  /* transform: none; */ /* Ensure no transform */
}

.bg2.active-tab:hover {
  background: linear-gradient(to bottom, #205A7C, #16405A); /* New selected hover background */
  border-color: #00C0FF; /* New selected hover border-color */
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.15), 0 0 12px rgba(0,191,255,0.5); /* New selected hover box-shadow */
}

/* Ensure selected text style priority - already covered by .check3 with !important and specific text-shadow */
.bg2.active-tab .button2.check3 {
  color: #FFFFFF !important; /* Explicitly ensuring for active tab text */
  text-shadow: 0 0 7px rgba(0, 180, 255, 0.7); /* Explicitly ensuring for active tab text */
  font-weight: 700;
  font-size: 15px;
}

.progress {
  /* position: fixed;
  bottom: 20px;
  right: 0px; */
  width: 100%;
  z-index: 100;
  height: 120px;
  padding: 20px 5px 20px 5px;
  border-radius: 5%;
  background: transparent;
  position: absolute;
  top: 770px;
  /* left: 200px; */
  /* margin:0 auto */
}

.el-button--small {
  background: transparent;
  border: 1px solid rgba(18, 137, 221, 1);
  color: #fff;
}

.progressStyle {
  width: 220px;
  display: inline-block;
  height: 20px;
  margin-right: 10px;
  margin-left: 20px;
}

.interval {
  margin-top: 0px;
  max-height: 30px;
  width: 100px;
  /* overflow: hidden; */
}

.el-select {
  width: 80px;
}

.el-select > .el-input {
  background: transparent !important;
}

:deep(.el-input__inner) {
  background: transparent !important;
  border: 1px solid rgba(18, 137, 221, 1);
  color: #fff;
}

:deep(.el-progress__text) {
  color: #fff !important;
}

.index {
  z-index: 120;
}

.el-button.is-disabled,
.el-button.is-disabled:focus,
.el-button.is-disabled:hover {
  background: transparent;
  border: 1px solid rgba(18, 137, 221, 1);
}

:deep(.el-input__inner) {
  height: 30px;
}

:deep(.el-select .el-input .el-select__caret) {
  position: absolute;
  top: 6px;
  right: 10px;
}

:deep(.el-select .el-input .el-select__caret.is-reverse) {
  position: absolute;
  top: -5px !important;
  right: 10px;
}

:deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.3);
}

.taskName {
  display: inline-block;
  margin-right: 20px;
  font-size: 45px;
  color: #fff;
}

.taskValue {
  display: inline-block;
  font-size: 45px;
  color: #fff;
}

.taskMsg {
  position: absolute;
  right: 150px;
  /* width: 120px; */
}

/* 图表滚动容器样式 */
.chart-scroll-container {
  margin-top: 15px; /* 减少与按钮组的间距 */
  background-color: rgba(10, 30, 55, 0.25);
  border-radius: 6px; /* 圆角 */
  padding: 8px 5px; /* 进一步减少内边距，增加图表可用空间 */
  /* 移除边框，减少视觉干扰 */
  /* border: 1px solid rgba(0, 190, 255, 0.15); */
  box-shadow: inset 0 0 10px rgba(0, 25, 50, 0.15); /* 减轻内阴影 */
}

/* echart2 容器的样式调整 */
div[ref="echart2"] {
  /* 移除之前的样式，现在由父容器控制 */
}

/* 自定义滚动条样式 */
.chart-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.chart-scroll-container::-webkit-scrollbar-track {
  background: rgba(0, 25, 50, 0.3);
  border-radius: 4px;
}

.chart-scroll-container::-webkit-scrollbar-thumb {
  background: rgba(0, 190, 255, 0.6);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.chart-scroll-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 190, 255, 0.8);
}

/* Firefox 滚动条样式 */
.chart-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 190, 255, 0.6) rgba(0, 25, 50, 0.3);
}

.chart-container {
  display: flex;
  align-items: center;
  height: 240px;
  /* background: linear-gradient(135deg,
    rgba(0, 12, 30, 0.97),
    rgba(0, 18, 40, 0.95)
  ); */
  /* background-color: #0A1E37; */ /* Previous opaque color */
  background-color: rgba(10, 30, 55, 0.25); /* Restored transparency */
  position: relative;
  border-radius: 12px;
  border: 1px solid rgba(0, 217, 255, 0.25);
  box-shadow:
    0 0 25px rgba(0, 0, 0, 0.3),
    inset 0 0 20px rgba(0, 217, 255, 0.05);
  overflow: hidden;
  /* backdrop-filter: blur(10px); */ /* 毛玻璃效果已移除 */
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

/* 任务按钮容器样式 */
.task-buttons-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.data-card {
  /* background: linear-gradient(135deg,
    rgba(0, 15, 35, 0.85),
    rgba(0, 25, 55, 0.8)
  ); */
  /* background-color: #0A1E37; */ /* Previous opaque color */
  background-color: rgba(10, 30, 55, 0.25); /* Restored transparency */
  border-radius: 10px;
  padding: 12px 10px;
  text-align: center;
  position: relative;
  box-shadow:
    0 5px 15px rgba(0, 0, 0, 0.25),
    inset 0 0 15px rgba(0, 217, 255, 0.05);
  border: 1px solid rgba(0, 217, 255, 0.2);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  /* backdrop-filter: blur(5px); */ /* 毛玻璃效果已移除 */
  margin-bottom: 10px;
  transform: translateZ(0);
}
