/**
 * 图表配置模块
 * 负责ECharts图表的配置和绘制逻辑
 */

export default {
  /**
   * 绘制折线图
   */
  drawLine() {
    const option = this.createLineChartOption()
    this.myChart && this.myChart.setOption(option, true)
  },

  /**
   * 绘制柱状图
   */
  drawLine2() {
    console.log('drawLine2 被调用')
    const option = this.createBarChartOption()
    console.log('createBarChartOption 返回的 option.series 长度:', option.series?.length)
    console.log('createBarChartOption 调试信息:')
    console.log('- compareId:', this.compareId)
    console.log('- selectedTasks:', this.selectedTasks)
    console.log('- selectedTasks.length:', this.selectedTasks?.length)
    console.log('- taskData.multiTaskData:', this.taskData.multiTaskData)
    console.log('- taskData.multiTaskData.length:', this.taskData.multiTaskData?.length)

    if (this.myChart2) {
      // 设置图表容器的实际高度
      const calculatedHeight = this.chartHeight
      console.log('设置图表高度:', calculatedHeight)

      // 动态设置图表容器的内容高度
      this.$refs.echart2.style.height = `${calculatedHeight}px`

      this.myChart2.setOption(option, true)
      // 确保图表在容器大小变化后能正确调整
      this.$nextTick(() => {
        this.myChart2.resize()
      })
    }
  },

  /**
   * 创建折线图配置
   */
  createLineChartOption() {
    // 确保即使没有数据也显示图表框架
    const hasData = this.Data.xAxis && this.Data.xAxis.length > 0

    return {
      title: {
        text: hasData ? '' : '暂无数据',
        textStyle: {
          color: '#fff',
          fontSize: 14
        },
        right: '40%',
        top: '30%',
        padding: [10, 0, 0, 10]
      },
      color: '#32c5ff',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
          lineStyle: {
            type: 'solid',
            color: 'rgba(0, 0, 0, 0)'
          }
        },
        formatter: function(params) {
          params = [params[0]]
          let htmlStr = ''
          htmlStr += '<div>'
          htmlStr += '<div>'
          htmlStr += params[0].axisValue + 'h'
          htmlStr += '</div>'
          htmlStr +=
            '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' +
            '#1890ff' +
            ';"></span>'
          htmlStr += params[0].seriesName + params[0].value
          htmlStr += '</div>'
          return htmlStr
        },
        backgroundColor: '#000033',
        textStyle: { color: '#fff' },
        borderWidth: 0
      },
      grid: {
        right: '5%',
        bottom: '25%',
        top: '12%',
        left: '10%'
      },
      xAxis: this.createXAxisConfig(),
      yAxis: this.createYAxisConfig(),
      series: [
        {
          name: '提交任务量',
          type: 'line',
          symbol: 'circle',
          symbolSize: 6,
          smooth: true,
          label: {
            show: false
          },
          zlevel: 1,
          z: 1,
          data: this.Data.yAxis || [],
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(50,197,255,0)'
                },
                {
                  offset: 1,
                  color: 'rgb(22,93,255)'
                }
              ],
              global: false
            }
          }
        }
      ],
      legend: {
        data: ['提交任务量'],
        left: 255,
        top: 225,
        itemHeight: 12,
        textStyle: {
          color: '#a1a1a1',
          fontSize: 12
        }
      },
      animation: true,
      animationDuration: function(idx) {
        return idx * 500
      },
      animationEasing: 'backln'
    }
  },

  /**
   * 创建X轴配置
   */
  createXAxisConfig() {
    return {
      type: 'category',
      boundaryGap: false,
      data: this.Data.xAxis || [],
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(1, 145, 255, 0.3)'
        }
      },
      axisLabel: {
        show: true,
        showMaxLabel: true,
        interval: 'auto',
        fontSize: '12',
        lineHeight: 40,
        color: 'rgba(255, 255, 255,1)',
        fontFamily: 'Microsoft YaHei',
        fontWeight: 'normal',
        formatter: function(value) {
          // 确保值显示为数字+h格式，无论输入是什么格式
          return value + 'h'
        }
      },
      axisTick: {
        show: false
      }
    }
  },

  /**
   * 创建Y轴配置
   */
  createYAxisConfig() {
    return {
      type: 'value',
      name: '',
      show: true,
      nameTextStyle: {
        padding: [0, 0, 0, 0]
      },
      splitLine: {
        show: true,
        lineStyle: {
          width: 0.5,
          type: 'dotted',
          color: 'rgba(1, 145, 255, 0.3)'
        }
      },
      nameGap: 10,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255,0.9)'
        }
      },
      axisLabel: {
        show: true,
        interval: 'auto',
        align: 'right',
        fontSize: '10',
        fontWeight: 'bold',
        color: 'rgba(255, 255, 255,0.9)'
      },
      scale: true,
      min: 0,
      splitNumber: 4
    }
  },

  /**
   * 切换任务类型
   */
  change(val) {
    this.taskType = val
    this.taskData = {
      xData: this.taskData.xData || [],
      used: [],
      used2: [],
      multiTaskData: this.taskData.multiTaskData || []
    }

    this.updateUIByTaskType(val)
    this.drawLine2()
  },

  /**
   * 调整图表大小
   */
  resize() {
    if (this.myChart) {
      this.myChart.resize()
    }
    if (this.myChart2) {
      this.myChart2.resize()
    }
  },

  /**
   * 创建柱状图配置
   * @returns {Object} 柱状图配置对象
   */
  createBarChartOption() {
    // 确保即使没有数据也显示图表框架
    const hasData = this.taskData.xData && this.taskData.xData.length > 0

    const option = {
      darkMode: true, // 启用暗黑模式
      title: {
        text: hasData ? '' : '暂无数据',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.6)', // 调整颜色
          fontSize: 14
        },
        left: 'center', // 居中
        top: 'center' // 居中
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow', // 更适合柱状图
          shadowStyle: {
            color: 'rgba(0, 190, 255, 0.05)'
          }
        },
        backgroundColor: 'rgba(0, 25, 50, 0.9)', // 深蓝背景
        borderColor: 'rgba(0, 190, 255, 0.7)', // 科技蓝边框
        borderWidth: 1,
        padding: [10, 15], // 调整内边距
        textStyle: {
          color: 'rgba(255, 255, 255, 0.95)',
          fontSize: 13
        },
        extraCssText: 'box-shadow: 0 3px 10px rgba(0, 150, 255, 0.35); border-radius: 4px;'
      },
      legend: {
        show: hasData,
        top: '5px', // 进一步减少顶部间距
        left: 'center',
        orient: 'horizontal',
        itemWidth: 14,
        itemHeight: 10,
        itemGap: 8,
        icon: 'rect',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.9)',
          fontSize: 11
        },
        inactiveColor: 'rgba(255, 255, 255, 0.4)',
        // 减少图例的内边距
        padding: [0, 0, 5, 0] // 上右下左，减少上下边距
      },
      grid: {
        left: '1%',
        right: '3%',
        top: '50px', // 减少顶部空间，紧凑布局
        bottom: '10px',
        containLabel: true
      },
      yAxis: {
        type: 'category',
        data: this.taskData.xData || [],
        show: hasData, // MODIFIED: Dynamically show/hide yAxis
        axisLine: {
          show: hasData, // MODIFIED: Dynamically show/hide yAxis.axisLine
          lineStyle: {
            color: 'rgba(0, 190, 255, 0.4)',
            width: 1
          }
        },
        axisTick: { show: hasData }, // MODIFIED: Dynamically show/hide yAxis.axisTick
        axisLabel: {
          show: hasData, // MODIFIED: Dynamically show/hide yAxis.axisLabel
          interval: 0,
          textStyle: {
            color: 'rgba(220, 220, 220, 0.95)',
            fontSize: 12,
            fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
          },
          margin: 12
        },
        splitLine: {
          show: hasData, // MODIFIED: Dynamically show/hide yAxis.splitLine
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed'
          }
        },
        // 添加斑马线背景 - 使用splitArea实现交替背景
        splitArea: {
          show: hasData,
          areaStyle: {
            color: [
              'rgba(0, 25, 50, 0.15)', // 第一种背景：深蓝色半透明，增加层次感
              'rgba(0, 120, 200, 0.06)' // 第二种背景：淡蓝色，降低透明度更加柔和
            ]
          }
        }
      },
      xAxis: {
        type: 'value',
        show: hasData, // MODIFIED: Dynamically show/hide xAxis
        axisLine: {
          show: hasData, // MODIFIED: Dynamically show/hide xAxis.axisLine
          lineStyle: {
            color: 'rgba(0, 190, 255, 0.4)',
            width: 1
          }
        },
        axisTick: { show: hasData }, // MODIFIED: Dynamically show/hide xAxis.axisTick
        axisLabel: {
          show: hasData, // MODIFIED: Dynamically show/hide xAxis.axisLabel
          textStyle: {
            color: 'rgba(220, 220, 220, 0.85)',
            fontSize: 11,
            fontFamily: '思源黑体, Microsoft YaHei, sans-serif'
          },
          margin: 10
        },
        splitLine: {
          show: hasData, // MODIFIED: Dynamically show/hide xAxis.splitLine
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.15)',
            type: 'dashed',
            width: 1
          }
        },
        scale: true,
        min: 0,
        splitNumber: 5
      },
      series: hasData ? this.createBarSeriesConfig() : [], // MODIFIED: Provide empty series array if no data
      // 柱状图间距配置 - 使用固定像素间距
      barCategoryGap: '15px', // 固定15px栏目间距，不随任务数量变化
      barGap: this.calculateBarGap(), // 动态计算同一类别内系列间距
      animation: true,
      animationDurationUpdate: 300,
      animationEasingUpdate: 'cubicInOut'
    }

    // 修复：正确处理多任务模式的系列数量限制
    // 如果是多任务模式（multiTaskData.length > 1），则不限制系列数量
    // 只有在真正的单任务模式下才限制为1个系列
    if (hasData &&
        (!this.taskData.multiTaskData || this.taskData.multiTaskData.length <= 1) &&
        this.compareId == 0 &&
        (!this.selectedTasks || this.selectedTasks.length <= 1)) {
      option.series.length = 1
    }

    return option
  },

  /**
   * 计算同一类别内系列间距
   * @returns {string} 系列间距百分比
   */
  calculateBarGap() {
    // 获取当前任务数量
    let taskCount = 1
    if (this.selectedTasks && this.selectedTasks.length > 0 && this.taskData.multiTaskData && this.taskData.multiTaskData.length > 0) {
      taskCount = this.taskData.multiTaskData.length
    } else if (this.compareId != 0 && this.taskData.used2 && this.taskData.used2.length > 0) {
      taskCount = 2
    }

    // 根据任务数量调整系列间距
    // 控制同一栏目内多个固定宽度柱子之间的间距
    const baseGap = 5 // 减少基础系列间距
    const reductionFactor = Math.max(0, (taskCount - 2) * 0.5) // 每增加一个任务减少0.5%间距
    const finalGap = Math.max(1, baseGap - reductionFactor) // 最小间距1%

    console.log('计算系列间距:', {
      taskCount,
      baseGap,
      reductionFactor,
      finalGap: finalGap + '%'
    })

    return finalGap + '%'
  },

  /**
   * 获取固定的柱子宽度
   * @param {number} taskCount - 任务数量
   * @returns {number} 固定的柱子宽度
   */
  getFixedBarWidth(taskCount) {
    // 设置固定宽度，保证良好的视觉效果
    const fixedWidth = 14 // 固定14px宽度，既美观又不会太宽

    console.log('使用固定柱子宽度:', {
      taskCount,
      fixedWidth
    })

    return fixedWidth
  },

  /**
   * 计算图表需要的高度
   * @param {number} cityCount - 城市数量
   * @param {number} taskCount - 任务数量
   * @returns {number} 计算出的图表高度
   */
  calculateChartHeight(cityCount, taskCount) {
    // 基础高度配置
    const baseHeight = 40 // 进一步减少基础高度

    // 根据任务数量动态调整每个城市项的高度
    // 任务越多，每个城市需要的高度越大
    let itemHeight = 30 // 减少单任务时的基础高度
    if (taskCount >= 2) {
      itemHeight = 30 + (taskCount - 1) * 15 // 进一步减少每个任务增加的高度到15px
    }

    const legendHeight = 30 // 进一步减少图例高度

    // 计算总高度
    const contentHeight = cityCount * itemHeight
    const totalHeight = baseHeight + contentHeight + legendHeight

    console.log('图表高度计算:', {
      cityCount,
      taskCount,
      itemHeight,
      contentHeight,
      totalHeight
    })

    return Math.max(350, totalHeight) // 减少最小高度到350px
  }
}
