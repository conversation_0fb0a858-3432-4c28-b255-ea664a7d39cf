/**
 * 数据管理模块
 * 负责数据初始化、重置、状态管理等功能
 */

export default {
  /**
   * 初始化空图表数据结构
   * @returns {Object} 包含初始化后的图表数据结构
   */
  initEmptyChartData() {
    return {
      // 折线图数据
      chartData: {
        xAxis: [],
        yAxis: []
      },
      // 柱状图数据
      barData: {
        xData: [],
        used: [],
        used2: [],
        multiTaskData: []
      },
      // 任务信息
      taskInfo: {
        SnapshotTime: 0,
        NCenters: 0,
        NPops: 0
      },
      // 任务统计
      taskStats: {
        totalNum: 0,
        execNum: 0
      },
      // 任务详情
      taskDetail: {
        ID: undefined,
        NJobs: undefined,
        SnapshotTime: undefined,
        CompletedFlag: undefined,
        strategy: undefined
      }
    }
  },

  /**
   * 重置所有图表数据
   */
  resetAllChartData() {
    const emptyData = this.initEmptyChartData();

    // 重置各数据结构
    this.Data = emptyData.chartData;
    this.taskData = emptyData.barData;
    this.msg = emptyData.taskInfo;
    this.msg2 = emptyData.taskStats;
    this.taskDetail = emptyData.taskDetail;

    // 重绘图表
    this.drawLine();
    this.drawLine2();

    // 重置状态
    this.count = 0;
    this.buttonShow = true;
    // 保持showButtom为true，以便显示暂停/继续按钮
    // this.showButtom = false; // 注释掉这行，保持按钮可见
    clearInterval(this.timer);
  },

  /**
   * 重置图表和任务数据
   */
  resetChartData() {
    // 初始化为空数据，仅保持图表框架
    this.Data = {
      xAxis: [],
      yAxis: []
    }

    this.taskData = {
      xData: [],
      used: [],
      used2: [],
      multiTaskData: []
    }

    this.drawLine()
    this.drawLine2()
    this.count = 0
    this.buttonShow = true
    // 保持showButtom为true，以便显示暂停/继续按钮
    // this.showButtom = false
  },

  /**
   * 重置主要信息和任务详情
   */
  resetTaskInfo() {
    this.msg = {
      SnapshotTime: 0,
      NCenters: 0,
      NPops: 0
    }
    this.msg2 = {
      totalNum: 0,
      execNum: 0
    }
    this.taskDetail = {
      ID: undefined,
      NJobs: undefined,
      SnapshotTime: undefined,
      CompletedFlag: undefined,
      strategy: undefined
    }
  },

  /**
   * 初始化任务类型数据对象
   */
  initTaskTypeData() {
    return {
      used: null,
      xData: [],
      used2: null
    }
  },

  /**
   * 更新指定类型的任务数据
   */
  updateTaskDataForType(city, usedData, usedData2) {
    this.taskData.xData = city
    this.taskData.used = usedData
    if (usedData2) {
      this.taskData.used2 = usedData2
    }
  },

  /**
   * 根据任务类型更新任务数据的通用方法
   */
  updateTaskDataByType(city, submitJobUsed, pendingJobUsed, resouceCompareUsed, index) {
    const idx = index !== undefined ? index : this.count

    if (this.taskType == 1) {
      this.updateTaskDataForType(city, submitJobUsed[idx], this.submitJobCompare?.used2?.[idx])
    }
    if (this.taskType == 2) {
      this.updateTaskDataForType(city, pendingJobUsed[idx], this.pendingJobCompare?.used2?.[idx])
    }
    if (this.taskType == 3) {
      this.updateTaskDataForType(city, resouceCompareUsed[idx], this.resouceCompare?.used2?.[idx])
    }
  },

  /**
   * 根据任务类型更新UI
   */
  updateUIByTaskType(type) {
    const isAtEnd = this.count == this.total
    const currentIndex = isAtEnd ? this.count - 1 : this.count

    // 更新选中状态
    this.check1 = type == 1
    this.check2 = type == 2
    this.check3 = type == 3

    // 设置任务名称
    const nameMap = {
      1: '提交任务量',
      2: '任务平均等待时长',
      3: '资源利用率对比'
    }
    this.name = nameMap[type]

    // 更新任务数据
    // 如果有多任务数据，优先使用多任务数据
    if (this.taskData.multiTaskData && this.taskData.multiTaskData.length > 0) {
      // 多任务模式：数据已经在multiTaskData中，不需要额外处理
      // createBarSeriesConfig会直接从multiTaskData中获取数据
    } else {
      // 兼容原有的双任务模式
      if (type == 1) {
        this.updateTaskDataForType(
          this.resouceCompare0?.xData || [],
          this.submitJobCompare0?.used?.[currentIndex],
          this.compareId !== 0 ? this.submitJobCompare?.used2?.[currentIndex] : undefined
        )
      } else if (type == 2) {
        this.updateTaskDataForType(
          this.pendingJobCompare0?.xData || [],
          this.pendingJobCompare0?.used?.[currentIndex],
          this.compareId !== 0 ? this.pendingJobCompare?.used2?.[currentIndex] : undefined
        )
      } else if (type == 3) {
        this.updateTaskDataForType(
          this.resouceCompare0?.xData || [],
          this.resouceCompare0?.used?.[currentIndex],
          this.compareId !== 0 ? this.resouceCompare?.used2?.[currentIndex] : undefined
        )
      }
    }
  },

  /**
   * 初始化基本任务信息
   */
  initBaseTaskInfo(res) {
    this.msg.SnapshotTime = 0
    this.msg.NPops = res.NPops
    this.msg.NCenters = res.NCenters
    this.taskDetail.SnapshotTime = res.SnapshotTime
    this.taskDetail.ID = res.ID
    this.taskDetail.strategy = res.Strategy
    this.taskDetail.NJobs = res.NJobs
    this.taskDetail.CompletedFlag = res.CompletedFlag
  },

  /**
   * 初始化主对比数据
   */
  initMainCompareData(city, submitJobCompare, pendingJobCompare, resouceCompare) {
    this.submitJobCompare0 = this.initTaskTypeData()
    this.pendingJobCompare0 = this.initTaskTypeData()
    this.resouceCompare0 = this.initTaskTypeData()

    this.submitJobCompare0.xData = city
    this.pendingJobCompare0.xData = city
    this.resouceCompare0.xData = city
    this.submitJobCompare0.used = submitJobCompare.used
    this.pendingJobCompare0.used = pendingJobCompare.used
    this.resouceCompare0.used = resouceCompare.used
  }
}
