/**
 * 工具函数模块
 * 包含时间格式化等工具函数
 */

export default {
  /**
   * 格式化时间（返回小时数）
   */
  formatDuring(val) {
    // 修改为只返回小时数，不使用天(d)为单位
    const totalHours = Math.floor(val * 1000 / (1000 * 60 * 60))
    return totalHours
  },

  /**
   * 格式化时间2（确保只返回小时数）
   */
  formatDuring2(val) {
    // 确保只返回小时数
    var hours = Math.floor(val * 3600 / 3600)
    return hours
  },

  /**
   * 格式化时间3（修改为只返回小时为单位的时间）
   */
  formatDuring3(val) {
    // 修改为只返回小时为单位的时间
    if (val < (1 / 3600)) {
      return (val * 3600).toFixed(2) + 's'
    }

    // 转换为小时，包括小数部分
    const totalHours = val
    return totalHours.toFixed(2) + 'h'
  }
}
