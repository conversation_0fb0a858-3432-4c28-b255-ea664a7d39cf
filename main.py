# main.py
import time

import uvicorn
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware

from app.api.v1 import locations, tasks, yaml_generate, job_groups
from app.core.config import settings
from app.core.log import logger

app = FastAPI(
    docs_url="/docs",
    redoc_url="/redoc",
    title="C2Net Simulation API",
    description="API for C2Net simulation system",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Router registration
app.include_router(locations.router, prefix="/api/v1/locations", tags=["locations"])
app.include_router(tasks.router, prefix="/api/v1/tasks", tags=["tasks"])
app.include_router(yaml_generate.router, prefix="/api/v1/yaml", tags=["yaml"])
app.include_router(job_groups.router, prefix="/api/v1/job", tags=["job"])


@app.middleware("http")
async def log_requests(request: Request, call_next):
    #

    start_time = time.time()
    # 调用实际的请求处理函数
    response = await call_next(request)
    # 计算处理时间
    process_time = time.time() - start_time
    # 构建自定义日志消息
    log_message = (
        f"{request.client.host:>16} - "
        f"{request.method:>5} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Process Time: {process_time:.3f}s"
    )
    logger.info(log_message)
    # 将处理时间添加到响应 headers
    response.headers["X-Process-Time"] = str(process_time)
    return response


logger.info(f"FastAPI application started：Debug mode: {settings.debug}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.http_port,
        reload=True,
        reload_delay=0.1,  # Add small delay between file change detection and reload
        workers=1,  # Ensure only one worker during development
        access_log=False,
    )
